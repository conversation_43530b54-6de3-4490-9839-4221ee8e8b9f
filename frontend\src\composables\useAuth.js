import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../store/modules/auth';
import { login as loginApi, register as registerApi, getCurrentUser } from '../api/auth';
import { ElMessage } from 'element-plus';

export function useAuth() {
  const authStore = useAuthStore();
  const router = useRouter();
  
  const loading = ref(false);
  const error = ref(null);
  
  // 计算属性
  const isLoggedIn = computed(() => authStore.isLoggedIn);
  const user = computed(() => authStore.user);
  const isAdmin = computed(() => user.value?.is_admin === 1);
  
  // 登录
  const login = async (credentials) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await loginApi(credentials);
      authStore.setToken(data.access_token);
      await fetchCurrentUser();
      ElMessage.success('登录成功');
      return true;
    } catch (err) {
      error.value = err.response?.data?.detail || '登录失败';
      ElMessage.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  };
  
  // 注册
  const register = async (userData) => {
    loading.value = true;
    error.value = null;
    
    try {
      await registerApi(userData);
      ElMessage.success('注册成功，请登录');
      return true;
    } catch (err) {
      error.value = err.response?.data?.detail || '注册失败';
      ElMessage.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!authStore.token) return null;
    
    loading.value = true;
    error.value = null;
    
    try {
      const userData = await getCurrentUser();
      authStore.setUser(userData);
      return userData;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取用户信息失败';
      if (err.response?.status === 401) {
        logout();
      }
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 登出
  const logout = () => {
    authStore.logout();
    ElMessage.success('已退出登录');
    router.push('/');
  };
  
  // 检查认证状态
  const checkAuth = async () => {
    if (authStore.token && !authStore.user) {
      await fetchCurrentUser();
    }
    return isLoggedIn.value;
  };
  
  return {
    loading,
    error,
    isLoggedIn,
    user,
    isAdmin,
    login,
    register,
    logout,
    checkAuth,
    fetchCurrentUser
  };
}
