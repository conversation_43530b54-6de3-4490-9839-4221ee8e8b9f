import logging
from typing import List, Optional, Tuple, Dict, Any

import numpy as np
from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.face import Face
from app.models.search import SearchHistory, SearchResult
from app.schemas.search import SearchHistoryCreate, SearchResultCreate
from app.utils.face_utils import (
    save_uploaded_image, 
    detect_faces, 
    deserialize_encoding,
    compare_faces
)

# 设置日志记录器
logger = logging.getLogger(__name__)


def get_search_history(db: Session, search_id: int) -> Optional[SearchHistory]:
    """
    通过ID获取搜索历史
    
    Args:
        db: 数据库会话
        search_id: 搜索历史ID
        
    Returns:
        Optional[SearchHistory]: 搜索历史对象，如果不存在则返回None
    """
    return db.query(SearchHistory).filter(SearchHistory.id == search_id).first()


def get_search_histories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    user_id: Optional[int] = None
) -> List[SearchHistory]:
    """
    获取搜索历史列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的最大记录数
        user_id: 用户ID（可选，用于筛选特定用户的搜索历史）
        
    Returns:
        List[SearchHistory]: 搜索历史列表
    """
    query = db.query(SearchHistory)
    if user_id is not None:
        query = query.filter(SearchHistory.user_id == user_id)
    return query.order_by(SearchHistory.created_at.desc()).offset(skip).limit(limit).all()


def create_search_history(
    db: Session, 
    search_data: SearchHistoryCreate
) -> SearchHistory:
    """
    创建搜索历史
    
    Args:
        db: 数据库会话
        search_data: 搜索历史创建模式
        
    Returns:
        SearchHistory: 创建的搜索历史对象
    """
    db_search = SearchHistory(
        image_path=search_data.image_path,
        user_id=search_data.user_id
    )
    
    db.add(db_search)
    db.commit()
    db.refresh(db_search)
    
    logger.info(f"Created search history: {db_search.id}")
    return db_search


def create_search_result(
    db: Session, 
    result_data: SearchResultCreate
) -> SearchResult:
    """
    创建搜索结果
    
    Args:
        db: 数据库会话
        result_data: 搜索结果创建模式
        
    Returns:
        SearchResult: 创建的搜索结果对象
    """
    db_result = SearchResult(
        search_id=result_data.search_id,
        face_id=result_data.face_id,
        similarity=result_data.similarity
    )
    
    db.add(db_result)
    db.commit()
    db.refresh(db_result)
    
    return db_result


def search_faces(
    db: Session, 
    file: UploadFile, 
    user_id: Optional[int] = None,
    threshold: float = 0.6
) -> SearchHistory:
    """
    搜索人脸
    
    Args:
        db: 数据库会话
        file: 上传的图片文件
        user_id: 用户ID（可选）
        threshold: 相似度阈值
        
    Returns:
        SearchHistory: 搜索历史对象，包含搜索结果
    """
    # 保存上传的图片
    image_path = save_uploaded_image(file, settings.UNKNOWN_FACES_DIR)
    
    # 检测人脸
    faces = detect_faces(image_path)
    
    # 使用第一个检测到的人脸
    face_info = faces[0]
    unknown_encoding = face_info["encoding"]
    
    # 创建搜索历史记录
    search_data = SearchHistoryCreate(
        image_path=image_path,
        user_id=user_id
    )
    search_history = create_search_history(db, search_data)
    
    # 获取所有已知人脸
    all_faces = db.query(Face).all()
    
    # 比较每个已知人脸
    results = []
    for face in all_faces:
        # 反序列化人脸编码
        known_encoding = deserialize_encoding(face.face_encoding)
        
        # 比较人脸
        _, similarity = compare_faces(known_encoding, unknown_encoding)
        
        # 如果相似度超过阈值，添加到结果中
        if similarity >= threshold:
            result_data = SearchResultCreate(
                search_id=search_history.id,
                face_id=face.id,
                similarity=similarity
            )
            result = create_search_result(db, result_data)
            results.append(result)
    
    # 刷新搜索历史以包含结果
    db.refresh(search_history)
    
    logger.info(f"Search completed: {search_history.id}, found {len(results)} matches")
    return search_history


def check_search_owner(db: Session, search_id: int, user_id: int) -> bool:
    """
    检查搜索历史是否属于指定用户
    
    Args:
        db: 数据库会话
        search_id: 搜索历史ID
        user_id: 用户ID
        
    Returns:
        bool: 搜索历史是否属于指定用户
    """
    db_search = get_search_history(db, search_id=search_id)
    if not db_search:
        return False
    
    # 如果搜索历史没有关联用户（匿名搜索），则任何人都不能声明所有权
    if db_search.user_id is None:
        return False
    
    return db_search.user_id == user_id
