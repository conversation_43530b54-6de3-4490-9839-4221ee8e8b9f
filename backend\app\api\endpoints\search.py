from typing import Any, List, Optional

from fastapi import APIRouter, Depends, File, Form, UploadFile
from sqlalchemy.orm import Session

from app.api.deps import (
    get_db_session, 
    get_current_active_user_dependency,
    get_current_admin_user_dependency
)
from app.core.exceptions import NotFoundException, ForbiddenException
from app.models.user import User
from app.schemas.search import SearchHistory, SearchRequest
from app.services.search import (
    get_search_history, 
    get_search_histories, 
    search_faces,
    check_search_owner
)

router = APIRouter()


@router.post("/", response_model=SearchHistory)
def search_face(
    file: UploadFile = File(...),
    search_request: SearchRequest = Depends(),
    current_user: Optional[User] = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    搜索人脸
    
    上传图片进行人脸搜索，返回搜索结果
    """
    # 如果用户已登录，关联搜索历史
    user_id = current_user.id if current_user else None
    
    # 执行搜索
    search_history = search_faces(
        db=db, 
        file=file, 
        user_id=user_id,
        threshold=search_request.threshold
    )
    
    return search_history


@router.get("/", response_model=List[SearchHistory])
def read_search_histories(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取搜索历史列表
    
    普通用户只能看到自己的搜索历史，管理员可以看到所有搜索历史
    """
    # 普通用户只能看到自己的搜索历史，管理员可以看到所有搜索历史
    user_id = None if current_user.is_admin == 1 else current_user.id
    histories = get_search_histories(db, skip=skip, limit=limit, user_id=user_id)
    return histories


@router.get("/{search_id}", response_model=SearchHistory)
def read_search_history(
    search_id: int,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取特定搜索历史
    
    普通用户只能看到自己的搜索历史，管理员可以看到所有搜索历史
    """
    search_history = get_search_history(db, search_id=search_id)
    if not search_history:
        raise NotFoundException("Search history not found")
    
    # 检查权限
    if current_user.is_admin != 1 and (search_history.user_id is None or search_history.user_id != current_user.id):
        raise ForbiddenException("Not enough permissions")
    
    return search_history
