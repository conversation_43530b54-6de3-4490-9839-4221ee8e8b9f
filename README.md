# 人脸识别搜索系统

基于FastAPI和Vue的人脸识别搜索系统，使用face_recognition库进行人脸识别，SQLite作为数据库。

## 功能特点

- 用户注册/登录系统
- 人脸库管理
  - 上传已知人脸图片
  - 查看/编辑/删除已知人脸信息
- 人脸搜索
  - 上传待搜索图片
  - 人脸检测与特征提取
  - 特征比对与搜索
  - 结果展示

## 技术栈

### 后端
- FastAPI: 高性能的Python Web框架
- SQLAlchemy: ORM框架
- face_recognition: 人脸识别库
- SQLite: 轻量级数据库

### 前端
- Vue 3: 渐进式JavaScript框架
- Element Plus: UI组件库
- Axios: HTTP客户端

## 项目结构

```
face_regenization/
├── backend/                # FastAPI 后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py        # FastAPI 主应用
│   │   ├── models.py      # 数据库模型
│   │   ├── schemas.py     # Pydantic 模型
│   │   ├── database.py    # 数据库连接
│   │   ├── crud.py        # CRUD 操作
│   │   └── face_utils.py  # 人脸识别工具函数
│   ├── requirements.txt   # 后端依赖
│   └── uploads/           # 上传的图片存储目录
│       ├── known/         # 已知人脸
│       └── unknown/       # 待搜索人脸
├── frontend/              # Vue 前端
│   ├── public/
│   ├── src/
│   │   ├── assets/
│   │   ├── components/
│   │   ├── views/
│   │   ├── App.vue
│   │   └── main.js
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 安装与运行

### 后端

1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

2. 运行服务器

```bash
cd backend
uvicorn app.main:app --reload
```

### 前端

1. 安装依赖

```bash
cd frontend
npm install
```

2. 运行开发服务器

```bash
cd frontend
npm run dev
```

## API文档

启动后端服务器后，访问 http://localhost:8000/docs 查看API文档。

## 使用流程

1. 注册/登录系统
2. 在"人脸库管理"页面上传已知人脸
3. 在"人脸搜索"页面上传待搜索图片
4. 查看搜索结果

## 注意事项

- 首次使用需要创建用户账号
- 上传的图片应包含清晰的人脸
- 系统性能取决于数据库中已知人脸的数量
