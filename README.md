# 人脸识别搜索系统

基于FastAPI和Vue的人脸识别搜索系统，使用face_recognition库进行人脸识别，SQLite作为数据库。

## 功能特点

- 用户注册/登录系统
- 人脸库管理
  - 上传已知人脸图片
  - 查看/编辑/删除已知人脸信息
- 人脸搜索
  - 上传待搜索图片
  - 人脸检测与特征提取
  - 特征比对与搜索
  - 结果展示

## 技术栈

### 后端
- FastAPI: 高性能的Python Web框架
- SQLAlchemy: ORM框架
- face_recognition: 人脸识别库
- SQLite: 轻量级数据库
- Pydantic: 数据验证
- JWT: 用户认证

### 前端
- Vue 3: 渐进式JavaScript框架
- Pinia: 状态管理
- Vue Router: 路由管理
- Element Plus: UI组件库
- Axios: HTTP客户端
- Vite: 构建工具

## 项目结构

```
face_regenization/
├── backend/                # FastAPI 后端
│   ├── app/
│   │   ├── api/            # API路由模块
│   │   │   ├── endpoints/  # API端点
│   │   │   └── router.py   # API路由注册
│   │   ├── core/           # 核心模块
│   │   ├── db/             # 数据库模块
│   │   ├── models/         # 数据库模型
│   │   ├── schemas/        # Pydantic模式
│   │   ├── services/       # 业务逻辑服务
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试
│   ├── uploads/            # 上传的图片存储目录
│   │   ├── known/          # 已知人脸
│   │   └── unknown/        # 待搜索人脸
│   ├── logs/               # 日志目录
│   ├── requirements/       # 依赖管理
│   ├── .env                # 环境变量
│   ├── Dockerfile          # Docker配置
│   └── main.py             # 应用入口
├── frontend/               # Vue 前端
│   ├── public/             # 静态资源
│   ├── src/
│   │   ├── api/            # API调用
│   │   ├── assets/         # 静态资源
│   │   ├── components/     # 通用组件
│   │   ├── composables/    # 组合式API
│   │   ├── router/         # 路由
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── views/          # 页面视图
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 入口文件
│   ├── .env                # 环境变量
│   ├── Dockerfile          # Docker配置
│   └── vite.config.js      # Vite配置
├── docker-compose.yml      # Docker Compose配置
└── README.md               # 项目说明
```

## 安装与运行

### 使用Docker Compose（推荐）

1. 确保已安装Docker和Docker Compose
2. 克隆项目并进入项目目录
3. 启动服务

```bash
docker-compose up -d
```

4. 访问应用：http://localhost

### 手动安装

#### 后端

1. 安装依赖

```bash
cd backend
pip install -r requirements/dev.txt
```

2. 运行服务器

```bash
cd backend
python run.py
```

#### 前端

1. 安装依赖

```bash
cd frontend
npm install
```

2. 运行开发服务器

```bash
cd frontend
npm run dev
```

## API文档

启动后端服务器后，访问 http://localhost:8000/api/v1/docs 查看API文档。

## 使用流程

1. 注册/登录系统
2. 在"人脸库管理"页面上传已知人脸
3. 在"人脸搜索"页面上传待搜索图片
4. 查看搜索结果

## 开发指南

### 后端开发

- 添加新的API端点：在`backend/app/api/endpoints/`目录下创建新文件，并在`router.py`中注册
- 添加新的数据模型：在`backend/app/models/`目录下创建新文件
- 添加新的业务逻辑：在`backend/app/services/`目录下创建新文件

### 前端开发

- 添加新的页面：在`frontend/src/views/`目录下创建新文件，并在`router/index.js`中注册
- 添加新的组件：在`frontend/src/components/`目录下创建新文件
- 添加新的API调用：在`frontend/src/api/`目录下创建或修改文件

## 注意事项

- 首次使用需要创建用户账号
- 上传的图片应包含清晰的人脸
- 系统性能取决于数据库中已知人脸的数量
