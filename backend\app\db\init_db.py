import logging
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import get_password_hash
from app.db.base import Base
from app.db.session import engine
from app.models.user import User
from app.schemas.user import UserCreate

# 设置日志记录器
logger = logging.getLogger(__name__)

# 初始数据
FIRST_SUPERUSER = "admin"
FIRST_SUPERUSER_EMAIL = "<EMAIL>"
FIRST_SUPERUSER_PASSWORD = "admin123"

def init_db(db: Session) -> None:
    """
    初始化数据库
    
    创建表并添加初始管理员用户
    
    Args:
        db: 数据库会话
    """
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    # 创建初始管理员用户
    user = db.query(User).filter(User.email == FIRST_SUPERUSER_EMAIL).first()
    if not user:
        user_in = UserCreate(
            username=FIRST_SUPERUSER,
            email=FIRST_SUPERUSER_EMAIL,
            password=FIRST_SUPERUSER_PASSWORD,
        )
        user = User(
            username=user_in.username,
            email=user_in.email,
            hashed_password=get_password_hash(user_in.password),
            is_admin=1,
        )
        db.add(user)
        db.commit()
        logger.info(f"Created initial admin user: {user.username}")
    else:
        logger.info(f"Admin user already exists: {user.username}")
