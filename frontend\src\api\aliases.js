import apiClient from './index';

/**
 * 获取别名列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的最大记录数
 * @param {number} params.face_id - 人脸ID（可选，用于筛选特定人脸的别名）
 * @returns {Promise} - 返回别名列表
 */
export const getAliases = async (params = {}) => {
  const response = await apiClient.get('/aliases/', { params });
  return response.data;
};

/**
 * 获取单个别名
 * @param {number} aliasId - 别名ID
 * @returns {Promise} - 返回别名信息
 */
export const getAlias = async (aliasId) => {
  const response = await apiClient.get(`/aliases/${aliasId}`);
  return response.data;
};

/**
 * 创建新别名
 * @param {Object} aliasData - 别名数据
 * @param {string} aliasData.name - 别名名称
 * @param {string} aliasData.description - 别名描述（可选）
 * @param {number} aliasData.face_id - 人脸ID
 * @returns {Promise} - 返回创建的别名信息
 */
export const createAlias = async (aliasData) => {
  const response = await apiClient.post('/aliases/', {
    name: aliasData.name,
    description: aliasData.description,
    face_id: aliasData.face_id
  });
  return response.data;
};

/**
 * 批量创建别名
 * @param {Object} data - 批量创建数据
 * @param {number} data.face_id - 人脸ID
 * @param {Array<Object>} data.aliases - 别名列表
 * @returns {Promise} - 返回创建的别名列表
 */
export const bulkCreateAliases = async (data) => {
  const response = await apiClient.post('/aliases/bulk', data);
  return response.data;
};

/**
 * 更新别名信息
 * @param {number} aliasId - 别名ID
 * @param {Object} aliasData - 别名数据
 * @param {string} aliasData.name - 别名名称
 * @param {string} aliasData.description - 别名描述
 * @returns {Promise} - 返回更新后的别名信息
 */
export const updateAlias = async (aliasId, aliasData) => {
  const response = await apiClient.put(`/aliases/${aliasId}`, aliasData);
  return response.data;
};

/**
 * 删除别名
 * @param {number} aliasId - 别名ID
 * @returns {Promise} - 返回删除结果
 */
export const deleteAlias = async (aliasId) => {
  const response = await apiClient.delete(`/aliases/${aliasId}`);
  return response.data;
};

/**
 * 批量删除别名
 * @param {Array<number>} aliasIds - 别名ID列表
 * @returns {Promise} - 返回删除结果
 */
export const bulkDeleteAliases = async (aliasIds) => {
  const response = await apiClient.delete('/aliases/bulk/delete', {
    data: { alias_ids: aliasIds }
  });
  return response.data;
};
