from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import (
    get_db_session, 
    get_current_active_user_dependency,
    get_current_admin_user_dependency
)
from app.core.exceptions import NotFoundException, ForbiddenException
from app.models.user import User
from app.schemas.category import Category, CategoryCreate, CategoryUpdate, FaceCategoryBulkUpdate
from app.services.category import (
    get_category, 
    get_categories, 
    create_category, 
    update_category, 
    delete_category,
    check_category_owner,
    add_faces_to_category,
    remove_faces_from_category,
    update_face_categories,
    bulk_update_face_categories
)

router = APIRouter()


@router.get("/", response_model=List[Category])
def read_categories(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取分类列表
    
    普通用户只能看到自己的分类，管理员可以看到所有分类
    """
    # 普通用户只能看到自己的分类，管理员可以看到所有分类
    user_id = None if current_user.is_admin == 1 else current_user.id
    categories = get_categories(db, skip=skip, limit=limit, user_id=user_id)
    return categories


@router.post("/", response_model=Category)
def create_new_category(
    category_data: CategoryCreate,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    创建新分类
    """
    return create_category(db=db, category_data=category_data, user_id=current_user.id)


@router.get("/{category_id}", response_model=Category)
def read_category(
    category_id: int,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取特定分类
    
    普通用户只能看到自己的分类，管理员可以看到所有分类
    """
    category = get_category(db, category_id=category_id)
    if not category:
        raise NotFoundException("Category not found")
    
    # 检查权限
    if current_user.is_admin != 1 and category.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    return category


@router.put("/{category_id}", response_model=Category)
def update_category_info(
    category_id: int,
    category_data: CategoryUpdate,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    更新分类信息
    
    普通用户只能更新自己的分类，管理员可以更新所有分类
    """
    category = get_category(db, category_id=category_id)
    if not category:
        raise NotFoundException("Category not found")
    
    # 检查权限
    if current_user.is_admin != 1 and category.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    updated_category = update_category(db=db, category_id=category_id, category_data=category_data)
    return updated_category


@router.delete("/{category_id}")
def delete_category_record(
    category_id: int,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    删除分类
    
    普通用户只能删除自己的分类，管理员可以删除所有分类
    """
    category = get_category(db, category_id=category_id)
    if not category:
        raise NotFoundException("Category not found")
    
    # 检查权限
    if current_user.is_admin != 1 and category.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    success = delete_category(db=db, category_id=category_id)
    if not success:
        raise NotFoundException("Category not found")
    
    return {"detail": "Category deleted successfully"}


@router.post("/{category_id}/faces")
def add_faces_to_category_endpoint(
    category_id: int,
    face_ids: List[int],
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    将人脸添加到分类中
    
    普通用户只能操作自己的分类，管理员可以操作所有分类
    """
    category = get_category(db, category_id=category_id)
    if not category:
        raise NotFoundException("Category not found")
    
    # 检查权限
    if current_user.is_admin != 1 and category.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    count = add_faces_to_category(db=db, category_id=category_id, face_ids=face_ids)
    return {"detail": f"Added {count} faces to category"}


@router.delete("/{category_id}/faces")
def remove_faces_from_category_endpoint(
    category_id: int,
    face_ids: List[int],
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    从分类中移除人脸
    
    普通用户只能操作自己的分类，管理员可以操作所有分类
    """
    category = get_category(db, category_id=category_id)
    if not category:
        raise NotFoundException("Category not found")
    
    # 检查权限
    if current_user.is_admin != 1 and category.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    count = remove_faces_from_category(db=db, category_id=category_id, face_ids=face_ids)
    return {"detail": f"Removed {count} faces from category"}


@router.post("/bulk-update")
def bulk_update_categories(
    data: FaceCategoryBulkUpdate,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    批量更新人脸分类
    
    将多个人脸同时分配到多个分类中
    """
    # 检查分类权限
    if current_user.is_admin != 1:
        for category_id in data.category_ids:
            category = get_category(db, category_id=category_id)
            if not category or category.user_id != current_user.id:
                raise ForbiddenException(f"Not enough permissions for category {category_id}")
    
    count = bulk_update_face_categories(db=db, face_ids=data.face_ids, category_ids=data.category_ids)
    return {"detail": f"Updated categories for {count} faces"}
