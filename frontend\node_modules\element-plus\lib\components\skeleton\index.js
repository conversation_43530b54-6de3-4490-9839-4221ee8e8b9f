'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var skeleton$1 = require('./src/skeleton2.js');
var skeletonItem$1 = require('./src/skeleton-item2.js');
var skeleton = require('./src/skeleton.js');
var skeletonItem = require('./src/skeleton-item.js');
var install = require('../../utils/vue/install.js');

const ElSkeleton = install.withInstall(skeleton$1["default"], {
  SkeletonItem: skeletonItem$1["default"]
});
const ElSkeletonItem = install.withNoopInstall(skeletonItem$1["default"]);

exports.skeletonProps = skeleton.skeletonProps;
exports.skeletonItemProps = skeletonItem.skeletonItemProps;
exports.ElSkeleton = ElSkeleton;
exports.ElSkeletonItem = ElSkeletonItem;
exports["default"] = ElSkeleton;
//# sourceMappingURL=index.js.map
