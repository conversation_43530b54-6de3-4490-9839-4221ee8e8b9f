import { ref, computed } from 'vue';
import { useFaceStore } from '../store/modules/faces';
import {
  getFaces,
  getFace,
  createFace,
  updateFace,
  deleteFace,
  bulkUploadFaces,
  updateFaceCategories,
  getFaceAliases,
  addFaceAliases
} from '../api/faces';
import { ElMessage, ElMessageBox } from 'element-plus';

export function useFaces() {
  const faceStore = useFaceStore();

  const loading = ref(false);
  const error = ref(null);
  const currentFace = ref(null);

  // 计算属性
  const faces = computed(() => faceStore.faces);
  const totalFaces = computed(() => faceStore.totalFaces);

  // 获取人脸列表
  const fetchFaces = async (params = {}) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await getFaces(params);
      faceStore.setFaces(data);
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取人脸列表失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 获取单个人脸
  const fetchFace = async (faceId) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await getFace(faceId);
      currentFace.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取人脸信息失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 添加人脸
  const addFace = async (faceData) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await createFace(faceData);
      faceStore.addFace(data);
      ElMessage.success('添加人脸成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加人脸失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新人脸
  const editFace = async (faceId, faceData) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await updateFace(faceId, faceData);
      faceStore.updateFace(data);
      ElMessage.success('更新人脸成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '更新人脸失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 删除人脸
  const removeFace = async (faceId) => {
    try {
      await ElMessageBox.confirm('确定要删除这个人脸吗？此操作不可恢复。', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      loading.value = true;
      error.value = null;

      await deleteFace(faceId);
      faceStore.removeFace(faceId);
      ElMessage.success('删除人脸成功');
      return true;
    } catch (err) {
      if (err !== 'cancel') {
        error.value = err.response?.data?.detail || '删除人脸失败';
        ElMessage.error(error.value);
      }
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 获取图片URL
  const getImageUrl = (path) => {
    if (!path) return '';

    // 从路径中提取文件名
    const filename = path.split('\\').pop().split('/').pop();

    // 构建URL
    return `${import.meta.env.VITE_API_BASE_URL || '/api/v1'}/uploads/known/${filename}`;
  };

  // 批量上传人脸
  const bulkUpload = async (files, categoryIds = null) => {
    loading.value = true;
    error.value = null;

    try {
      const data = {
        files: files
      };

      if (categoryIds && categoryIds.length > 0) {
        data.category_ids = categoryIds;
      }

      const result = await bulkUploadFaces(data);

      // 更新本地数据
      if (result.faces && result.faces.length > 0) {
        result.faces.forEach(face => {
          faceStore.addFace(face);
        });
      }

      ElMessage.success(`批量上传成功: ${result.success}/${result.total} 个人脸`);
      return result;
    } catch (err) {
      error.value = err.response?.data?.detail || '批量上传人脸失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新人脸分类
  const updateCategories = async (faceId, categoryIds) => {
    loading.value = true;
    error.value = null;

    try {
      const result = await updateFaceCategories(faceId, categoryIds);
      ElMessage.success('更新人脸分类成功');
      return result;
    } catch (err) {
      error.value = err.response?.data?.detail || '更新人脸分类失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 获取人脸别名
  const getAliases = async (faceId) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await getFaceAliases(faceId);
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取人脸别名失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 添加人脸别名
  const addAliases = async (faceId, aliases) => {
    loading.value = true;
    error.value = null;

    try {
      const data = await addFaceAliases(faceId, aliases);
      ElMessage.success('添加人脸别名成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加人脸别名失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    faces,
    totalFaces,
    currentFace,
    fetchFaces,
    fetchFace,
    addFace,
    editFace,
    removeFace,
    getImageUrl,
    bulkUpload,
    updateCategories,
    getAliases,
    addAliases
  };
}
