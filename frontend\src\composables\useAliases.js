import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getAliases,
  getAlias,
  createAlias,
  bulkCreateAliases,
  updateAlias,
  deleteAlias,
  bulkDeleteAliases
} from '../api/aliases';
import { getFaceAliases, addFaceAliases } from '../api/faces';

export function useAliases() {
  const loading = ref(false);
  const error = ref(null);
  const aliases = ref([]);
  const currentAlias = ref(null);
  
  // 获取别名列表
  const fetchAliases = async (params = {}) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getAliases(params);
      aliases.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取别名列表失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  // 获取人脸的别名
  const fetchFaceAliases = async (faceId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getFaceAliases(faceId);
      aliases.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取人脸别名失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  // 获取单个别名
  const fetchAlias = async (aliasId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getAlias(aliasId);
      currentAlias.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取别名信息失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 添加别名
  const addAlias = async (aliasData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await createAlias(aliasData);
      aliases.value.push(data);
      ElMessage.success('添加别名成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加别名失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 为人脸添加别名
  const addAliasesToFace = async (faceId, aliasesData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await addFaceAliases(faceId, aliasesData);
      ElMessage.success('添加别名成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加别名失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 批量添加别名
  const bulkAddAliases = async (faceId, aliasesData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await bulkCreateAliases({
        face_id: faceId,
        aliases: aliasesData
      });
      ElMessage.success('批量添加别名成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '批量添加别名失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 更新别名
  const editAlias = async (aliasId, aliasData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await updateAlias(aliasId, aliasData);
      
      // 更新本地数据
      const index = aliases.value.findIndex(a => a.id === aliasId);
      if (index !== -1) {
        aliases.value[index] = data;
      }
      
      ElMessage.success('更新别名成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '更新别名失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 删除别名
  const removeAlias = async (aliasId) => {
    try {
      await ElMessageBox.confirm('确定要删除这个别名吗？此操作不可恢复。', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      loading.value = true;
      error.value = null;
      
      await deleteAlias(aliasId);
      
      // 更新本地数据
      aliases.value = aliases.value.filter(a => a.id !== aliasId);
      
      ElMessage.success('删除别名成功');
      return true;
    } catch (err) {
      if (err !== 'cancel') {
        error.value = err.response?.data?.detail || '删除别名失败';
        ElMessage.error(error.value);
      }
      return false;
    } finally {
      loading.value = false;
    }
  };
  
  // 批量删除别名
  const bulkRemoveAliases = async (aliasIds) => {
    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${aliasIds.length} 个别名吗？此操作不可恢复。`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      loading.value = true;
      error.value = null;
      
      const result = await bulkDeleteAliases(aliasIds);
      
      // 更新本地数据
      aliases.value = aliases.value.filter(a => !aliasIds.includes(a.id));
      
      ElMessage.success('批量删除别名成功');
      return result;
    } catch (err) {
      if (err !== 'cancel') {
        error.value = err.response?.data?.detail || '批量删除别名失败';
        ElMessage.error(error.value);
      }
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  return {
    loading,
    error,
    aliases,
    currentAlias,
    fetchAliases,
    fetchFaceAliases,
    fetchAlias,
    addAlias,
    addAliasesToFace,
    bulkAddAliases,
    editAlias,
    removeAlias,
    bulkRemoveAliases
  };
}
