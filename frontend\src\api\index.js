import axios from 'axios';
import { useAuthStore } from '../store/modules/auth';

// 1. 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 2. 请求拦截器：添加认证信息
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 3. 响应拦截器：统一错误处理
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    const authStore = useAuthStore();
    
    // 处理401未授权错误
    if (error.response?.status === 401) {
      authStore.logout();
      window.location.href = '/';
    }
    
    // 处理其他错误
    error.message = error.response?.data?.detail || '请求失败';
    return Promise.reject(error);
  }
);

export default apiClient;
