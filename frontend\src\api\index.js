import axios from 'axios';
import { useAuthStore } from '../store/modules/auth';

// 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    const token = authStore.token;

    // 如果有token，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const authStore = useAuthStore();
    
    // 打印详细错误信息
    console.error('API Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      headers: error.config?.headers
    });

    // 如果是401错误，清除token并重定向到登录页
    if (error.response && error.response.status === 401) {
      authStore.logout();
      window.location.href = '/';
    }
    
    // 统一错误处理
    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 400:
          error.message = error.response.data.detail || '请求参数错误';
          break;
        case 401:
          error.message = '未授权，请重新登录';
          break;
        case 403:
          error.message = '拒绝访问';
          break;
        case 404:
          error.message = '请求错误，未找到该资源';
          break;
        case 408:
          error.message = '请求超时';
          break;
        case 500:
          error.message = '服务器端错误';
          break;
        case 501:
          error.message = '网络未实现';
          break;
        case 502:
          error.message = '网络错误';
          break;
        case 503:
          error.message = '服务不可用';
          break;
        case 504:
          error.message = '网络超时';
          break;
        case 505:
          error.message = 'HTTP版本不支持该请求';
          break;
        default:
          error.message = `连接错误${error.response.status}`;
      }
    } else {
      // 请求被取消或者网络问题
      error.message = '连接到服务器失败';
    }

    return Promise.reject(error);
  }
);

export default apiClient;
