from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import os
import jwt
from jwt.exceptions import PyJWTError

from . import models, schemas, crud
from .database import engine, get_db
from .face_utils import (
    detect_faces, save_uploaded_image, 
    KNOWN_FACES_DIR, UNKNOWN_FACES_DIR
)

# 创建数据库表
models.Base.metadata.create_all(bind=engine)

# 创建FastAPI应用
app = FastAPI(title="人脸识别搜索系统")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# JWT配置
SECRET_KEY = "your-secret-key"  # 生产环境应使用安全的密钥
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 创建访问令牌
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 获取当前用户
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = schemas.TokenData(username=username)
    except PyJWTError:
        raise credentials_exception
    user = crud.get_user_by_username(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# 获取当前活跃用户
async def get_current_active_user(current_user: schemas.User = Depends(get_current_user)):
    return current_user

# 获取当前管理员用户
async def get_current_admin_user(current_user: schemas.User = Depends(get_current_user)):
    if current_user.is_admin != 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )
    return current_user

# 登录获取令牌
@app.post("/token", response_model=schemas.Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = crud.get_user_by_username(db, username=form_data.username)
    if not user or not crud.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# 用户注册
@app.post("/users/", response_model=schemas.User)
def create_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    db_user = crud.get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    db_user = crud.get_user_by_username(db, username=user.username)
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    return crud.create_user(db=db, user=user)

# 获取当前用户信息
@app.get("/users/me/", response_model=schemas.User)
async def read_users_me(current_user: schemas.User = Depends(get_current_active_user)):
    return current_user

# 上传已知人脸
@app.post("/faces/", response_model=schemas.Face)
async def create_face(
    name: str = Form(...),
    file: UploadFile = File(...),
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # 保存上传的图片
    image_path = save_uploaded_image(file, KNOWN_FACES_DIR)
    
    # 检测人脸
    face_encodings = detect_faces(image_path)
    
    if not face_encodings:
        # 如果没有检测到人脸，删除图片并返回错误
        os.remove(image_path)
        raise HTTPException(status_code=400, detail="No face detected in the image")
    
    # 使用第一个检测到的人脸
    face_encoding = face_encodings[0]
    
    # 创建人脸记录
    face = schemas.FaceCreate(name=name)
    db_face = crud.create_face(
        db=db, 
        face=face, 
        image_path=image_path, 
        face_encoding=face_encoding, 
        user_id=current_user.id
    )
    
    return db_face

# 获取已知人脸列表
@app.get("/faces/", response_model=List[schemas.Face])
def read_faces(
    skip: int = 0, 
    limit: int = 100, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # 普通用户只能看到自己的人脸，管理员可以看到所有人脸
    user_id = None if current_user.is_admin == 1 else current_user.id
    faces = crud.get_faces(db, skip=skip, limit=limit, user_id=user_id)
    return faces

# 获取单个人脸
@app.get("/faces/{face_id}", response_model=schemas.Face)
def read_face(
    face_id: int, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_face = crud.get_face(db, face_id=face_id)
    if db_face is None:
        raise HTTPException(status_code=404, detail="Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and db_face.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return db_face

# 更新人脸信息
@app.put("/faces/{face_id}", response_model=schemas.Face)
def update_face(
    face_id: int, 
    face: schemas.FaceCreate, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_face = crud.get_face(db, face_id=face_id)
    if db_face is None:
        raise HTTPException(status_code=404, detail="Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and db_face.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return crud.update_face(db=db, face_id=face_id, face=face)

# 删除人脸
@app.delete("/faces/{face_id}")
def delete_face(
    face_id: int, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_face = crud.get_face(db, face_id=face_id)
    if db_face is None:
        raise HTTPException(status_code=404, detail="Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and db_face.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    success = crud.delete_face(db=db, face_id=face_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete face")
    
    return {"detail": "Face deleted successfully"}

# 搜索人脸
@app.post("/search/", response_model=schemas.SearchHistory)
async def search_face(
    file: UploadFile = File(...),
    current_user: Optional[schemas.User] = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # 保存上传的图片
    image_path = save_uploaded_image(file, UNKNOWN_FACES_DIR)
    
    # 检测人脸
    face_encodings = detect_faces(image_path)
    
    if not face_encodings:
        # 如果没有检测到人脸，删除图片并返回错误
        os.remove(image_path)
        raise HTTPException(status_code=400, detail="No face detected in the image")
    
    # 使用第一个检测到的人脸
    face_encoding = face_encodings[0]
    
    # 创建搜索历史记录
    user_id = current_user.id if current_user else None
    search_history = crud.create_search_history(db=db, image_path=image_path, user_id=user_id)
    
    # 搜索匹配的人脸
    matches = crud.search_faces(db=db, unknown_encoding=face_encoding)
    
    # 保存搜索结果
    for face, similarity in matches:
        crud.create_search_result(
            db=db, 
            search_id=search_history.id, 
            face_id=face.id, 
            similarity=similarity
        )
    
    # 获取完整的搜索历史（包括结果）
    return crud.get_search_history(db=db, search_id=search_history.id)

# 获取搜索历史
@app.get("/search/", response_model=List[schemas.SearchHistory])
def read_search_histories(
    skip: int = 0, 
    limit: int = 100, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # 普通用户只能看到自己的搜索历史，管理员可以看到所有搜索历史
    user_id = None if current_user.is_admin == 1 else current_user.id
    histories = crud.get_search_histories(db, skip=skip, limit=limit, user_id=user_id)
    return histories

# 获取单个搜索历史
@app.get("/search/{search_id}", response_model=schemas.SearchHistory)
def read_search_history(
    search_id: int, 
    current_user: schemas.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_search = crud.get_search_history(db, search_id=search_id)
    if db_search is None:
        raise HTTPException(status_code=404, detail="Search history not found")
    
    # 检查权限
    if current_user.is_admin != 1 and db_search.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return db_search

# 健康检查
@app.get("/health")
def health_check():
    return {"status": "ok"}
