import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  addFacesToCategory,
  removeFacesFromCategory,
  bulkUpdateCategories
} from '../api/categories';

export function useCategories() {
  const loading = ref(false);
  const error = ref(null);
  const categories = ref([]);
  const currentCategory = ref(null);
  
  // 获取分类列表
  const fetchCategories = async (params = {}) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getCategories(params);
      categories.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取分类列表失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  // 获取单个分类
  const fetchCategory = async (categoryId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getCategory(categoryId);
      currentCategory.value = data;
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取分类信息失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 添加分类
  const addCategory = async (categoryData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await createCategory(categoryData);
      categories.value.push(data);
      ElMessage.success('添加分类成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加分类失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 更新分类
  const editCategory = async (categoryId, categoryData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await updateCategory(categoryId, categoryData);
      
      // 更新本地数据
      const index = categories.value.findIndex(c => c.id === categoryId);
      if (index !== -1) {
        categories.value[index] = data;
      }
      
      ElMessage.success('更新分类成功');
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '更新分类失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 删除分类
  const removeCategory = async (categoryId) => {
    try {
      await ElMessageBox.confirm('确定要删除这个分类吗？此操作不可恢复。', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      loading.value = true;
      error.value = null;
      
      await deleteCategory(categoryId);
      
      // 更新本地数据
      categories.value = categories.value.filter(c => c.id !== categoryId);
      
      ElMessage.success('删除分类成功');
      return true;
    } catch (err) {
      if (err !== 'cancel') {
        error.value = err.response?.data?.detail || '删除分类失败';
        ElMessage.error(error.value);
      }
      return false;
    } finally {
      loading.value = false;
    }
  };
  
  // 将人脸添加到分类
  const addFacesToCategoryAction = async (categoryId, faceIds) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await addFacesToCategory(categoryId, faceIds);
      ElMessage.success('添加人脸到分类成功');
      return result;
    } catch (err) {
      error.value = err.response?.data?.detail || '添加人脸到分类失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 从分类中移除人脸
  const removeFacesFromCategoryAction = async (categoryId, faceIds) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await removeFacesFromCategory(categoryId, faceIds);
      ElMessage.success('从分类中移除人脸成功');
      return result;
    } catch (err) {
      error.value = err.response?.data?.detail || '从分类中移除人脸失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 批量更新人脸分类
  const bulkUpdateCategoriesAction = async (faceIds, categoryIds) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await bulkUpdateCategories({
        face_ids: faceIds,
        category_ids: categoryIds
      });
      ElMessage.success('批量更新分类成功');
      return result;
    } catch (err) {
      error.value = err.response?.data?.detail || '批量更新分类失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  return {
    loading,
    error,
    categories,
    currentCategory,
    fetchCategories,
    fetchCategory,
    addCategory,
    editCategory,
    removeCategory,
    addFacesToCategoryAction,
    removeFacesFromCategoryAction,
    bulkUpdateCategoriesAction
  };
}
