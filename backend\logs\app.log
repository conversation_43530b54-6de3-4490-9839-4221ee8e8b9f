2025-05-22 16:49:33,526 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\CODE\pythonProject\face_regenization\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-22 16:49:33,832 - app.db.init_db - INFO - Created initial admin user: admin
2025-05-22 16:49:33,832 - backend.main - INFO - Application startup complete
2025-05-23 08:34:38,419 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 08:34:38,419 - backend.main - INFO - Application startup complete
2025-05-23 11:37:55,082 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 11:37:55,082 - backend.main - INFO - Application startup complete
2025-05-23 12:16:52,278 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\CODE\pythonProject\face_regenization\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-23 13:36:40,495 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_133640_Screenshot 2025-05-15 151007.png
2025-05-23 13:39:21,136 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 13:39:21,136 - backend.main - INFO - Application startup complete
2025-05-23 13:39:29,814 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_133929_Screenshot 2025-05-15 151007.png
2025-05-23 13:39:37,724 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_133937_Screenshot 2025-05-15 151007.png
2025-05-23 13:40:38,580 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134038_Screenshot 2025-05-15 151007.png
2025-05-23 13:42:22,375 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134222_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:44:21,661 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 13:44:21,662 - backend.main - INFO - Application startup complete
2025-05-23 13:44:23,088 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134423_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:44:23,097 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134423_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 13:44:55,143 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134455_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:44:55,162 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134455_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 13:47:45,505 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134745_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:47:45,522 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134745_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 13:49:21,784 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 13:49:21,784 - backend.main - INFO - Application startup complete
2025-05-23 13:49:34,678 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134934_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:49:34,678 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134934_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:49:34,678 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134934_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:49:34,688 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 13:49:34,688 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 13:49:34,693 - app.utils.face_utils - INFO - Converting to NumPy array...
2025-05-23 13:49:34,696 - app.utils.face_utils - INFO - NumPy array shape: (760, 856, 3), dtype: uint8
2025-05-23 13:49:34,699 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_134934_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 13:54:21,991 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 13:54:21,992 - backend.main - INFO - Application startup complete
2025-05-23 13:54:29,089 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 13:54:29,090 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 13:54:29,092 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:29,095 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:29,095 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:29,096 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:29,106 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 13:54:29,107 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 13:54:29,111 - app.utils.face_utils - INFO - Saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg_temp.jpg
2025-05-23 13:54:29,112 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 13:54:29,126 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 13:54:29,130 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135429_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 13:54:57,309 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 13:54:57,309 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 13:54:57,313 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:57,318 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:57,318 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:57,318 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg
2025-05-23 13:54:57,330 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 13:54:57,330 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 13:54:57,340 - app.utils.face_utils - INFO - Saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg_temp.jpg
2025-05-23 13:54:57,340 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 13:54:57,359 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 13:54:57,359 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_135457_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:23:39,738 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:23:39,738 - backend.main - INFO - Application startup complete
2025-05-23 14:23:43,404 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:23:43,405 - backend.main - INFO - Application startup complete
2025-05-23 14:24:16,572 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:24:16,572 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 14:24:16,576 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:24:16,581 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:24:16,581 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:24:16,581 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:24:16,594 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:24:16,594 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 14:24:16,605 - app.utils.face_utils - INFO - Saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg_temp.jpg
2025-05-23 14:24:16,605 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 14:24:16,623 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:24:16,628 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142416_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:27:10,291 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:27:10,291 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 14:27:10,295 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:27:10,297 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:27:10,297 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:27:10,297 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:27:10,307 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:27:10,307 - app.utils.face_utils - INFO - Normalizing image...
2025-05-23 14:27:10,313 - app.utils.face_utils - INFO - Saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg_temp.jpg
2025-05-23 14:27:10,313 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 14:27:10,327 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:27:10,327 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142710_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:28:43,955 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:28:43,955 - backend.main - INFO - Application startup complete
2025-05-23 14:29:22,204 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:29:22,204 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB
2025-05-23 14:29:22,211 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:29:22,213 - app.utils.face_utils - INFO - Saving preprocessed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142922_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:22,215 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142922_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:22,215 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142922_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:22,216 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142922_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:22,216 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 14:29:22,231 - app.utils.face_utils - INFO - Image shape: (760, 856, 3), dtype: uint8
2025-05-23 14:29:22,231 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:29:22,234 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142922_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:29:25,683 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:29:25,684 - backend.main - INFO - Application startup complete
2025-05-23 14:29:33,938 - app.utils.face_utils - INFO - Original image mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:29:33,938 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB
2025-05-23 14:29:33,943 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:29:33,944 - app.utils.face_utils - INFO - Saving preprocessed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142933_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:33,953 - app.utils.face_utils - INFO - Saved and processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142933_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:33,953 - app.utils.face_utils - INFO - Saved uploaded image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142933_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:33,953 - app.utils.face_utils - INFO - Opening image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142933_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:29:33,962 - app.utils.face_utils - INFO - Processing image for face detection - Mode: RGB, Size: (856, 760)
2025-05-23 14:29:33,962 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB
2025-05-23 14:29:33,966 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:29:33,966 - app.utils.face_utils - INFO - Preprocessed image shape: (760, 856, 3), dtype: uint8
2025-05-23 14:29:33,966 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:29:33,970 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_142933_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:33:43,439 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:33:43,439 - backend.main - INFO - Application startup complete
2025-05-23 14:36:25,742 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:36:25,743 - backend.main - INFO - Application startup complete
2025-05-23 14:36:38,131 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-23 14:36:38,141 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_143638_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:36:38,142 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-23 14:36:38,142 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:36:38,142 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:36:38,146 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:36:38,146 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3)
2025-05-23 14:36:38,151 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_143638_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:36:38,152 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_143638_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:36:38,166 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:36:38,166 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:36:38,170 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:36:38,170 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3)
2025-05-23 14:36:38,171 - app.utils.face_utils - INFO - Image array ready for face detection - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:36:38,171 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:36:38,174 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_143638_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:40:01,369 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-23 14:40:01,369 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144001_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:40:01,369 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-23 14:40:01,369 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:40:01,370 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:40:01,376 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:40:01,376 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3)
2025-05-23 14:40:01,384 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144001_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:40:01,385 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144001_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:40:01,399 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:40:01,399 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:40:01,406 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:40:01,406 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3)
2025-05-23 14:40:01,406 - app.utils.face_utils - INFO - Image array ready for face detection - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:40:01,406 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:40:01,407 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144001_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:40:13,862 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:40:13,863 - backend.main - INFO - Application startup complete
2025-05-23 14:41:41,402 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-23 14:41:41,426 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144141_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:41:41,427 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-23 14:41:41,427 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:41:41,427 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:41:41,436 - app.utils.face_utils - INFO - Image extrema values: ((16, 255), (8, 248), (0, 255))
2025-05-23 14:41:41,440 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:41:41,443 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-23 14:41:41,455 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144141_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:41:41,455 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144141_Screenshot 2025-05-15 151007.jpg
2025-05-23 14:41:41,466 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-23 14:41:41,467 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-23 14:41:41,473 - app.utils.face_utils - INFO - Image extrema values: ((16, 255), (8, 248), (0, 255))
2025-05-23 14:41:41,475 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:41:41,476 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-23 14:41:41,476 - app.utils.face_utils - INFO - Image array ready for face detection - Shape: (760, 856, 3), dtype: uint8
2025-05-23 14:41:41,476 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 14:41:41,481 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_144141_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 14:45:14,744 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:45:14,744 - backend.main - INFO - Application startup complete
2025-05-23 14:50:14,671 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 14:50:14,671 - backend.main - INFO - Application startup complete
2025-05-23 15:40:40,180 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 15:40:40,180 - backend.main - INFO - Application startup complete
2025-05-23 15:40:43,532 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 15:40:43,533 - backend.main - INFO - Application startup complete
2025-05-23 15:40:55,763 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\CODE\pythonProject\face_regenization\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-23 15:41:09,396 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 15:41:09,412 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_154109_Screenshot 2025-05-15 151122.jpg
2025-05-23 15:41:09,413 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 15:41:09,413 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 15:41:09,413 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 15:41:09,420 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_154109_Screenshot 2025-05-15 151122.jpg
2025-05-23 15:41:09,427 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_154109_Screenshot 2025-05-15 151122.jpg
2025-05-23 15:41:09,427 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_154109_Screenshot 2025-05-15 151122.jpg
2025-05-23 15:41:09,427 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-23 15:41:09,445 - app.utils.face_utils - INFO - Image loaded - Shape: (333, 426, 3), dtype: uint8
2025-05-23 15:41:09,445 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 15:41:09,453 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_154109_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 15:45:43,831 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 15:45:43,831 - backend.main - INFO - Application startup complete
2025-05-23 16:01:07,305 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 16:01:07,316 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:01:07,317 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 16:01:07,317 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 16:01:07,317 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 16:01:07,319 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:01:07,321 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:01:07,322 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:01:07,334 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-23 16:01:07,337 - app.utils.face_utils - INFO - Saved preprocessed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:01:07,337 - app.utils.face_utils - INFO - Loading preprocessed image with face_recognition...
2025-05-23 16:01:07,349 - app.utils.face_utils - INFO - Image loaded - Shape: (333, 426, 3), dtype: uint8
2025-05-23 16:01:07,350 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 16:01:07,353 - app.utils.face_utils - INFO - Removed temporary file D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:01:07,353 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160107_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 16:08:12,051 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 16:08:12,051 - backend.main - INFO - Application startup complete
2025-05-23 16:08:22,225 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 16:08:22,236 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:08:22,237 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 16:08:22,237 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 16:08:22,237 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 16:08:22,240 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:08:22,242 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:08:22,244 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:08:22,255 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG, Bits: 8
2025-05-23 16:08:22,258 - app.utils.face_utils - INFO - Saved preprocessed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:08:22,267 - app.utils.face_utils - INFO - Temp image info - Mode: RGB, Size: (426, 333), Bits: 8
2025-05-23 16:08:22,271 - app.utils.face_utils - INFO - Image array - Shape: (333, 426, 3), dtype: uint8, min: 0, max: 249
2025-05-23 16:08:22,271 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 16:08:22,275 - app.utils.face_utils - INFO - Removed temporary file D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:08:22,275 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_160822_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 16:10:56,827 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 16:10:56,827 - backend.main - INFO - Application startup complete
2025-05-23 16:11:01,033 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 16:11:01,043 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:11:01,044 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 16:11:01,044 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 16:11:01,044 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 16:11:01,046 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:11:01,048 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:11:01,062 - app.utils.face_utils - INFO - Saved image info - Mode: RGB, Size: (426, 333), Bits: 8
2025-05-23 16:11:01,062 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:11:01,062 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG, Bits: 8
2025-05-23 16:11:01,064 - app.utils.face_utils - INFO - Saved preprocessed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:11:01,073 - app.utils.face_utils - INFO - Temp image info - Mode: RGB, Size: (426, 333), Bits: 8
2025-05-23 16:11:01,075 - app.utils.face_utils - INFO - Image array - Shape: (333, 426, 3), dtype: uint8, min: 0, max: 249
2025-05-23 16:11:01,075 - app.utils.face_utils - INFO - Image is RGB format (24-bit, 8-bit per channel)
2025-05-23 16:11:01,075 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 16:11:01,078 - app.utils.face_utils - INFO - Removed temporary file D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg_temp.jpg
2025-05-23 16:11:01,078 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_161101_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 16:14:24,739 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 16:14:24,739 - backend.main - INFO - Application startup complete
2025-05-23 16:59:50,122 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 16:59:50,139 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_165950_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:59:50,140 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 16:59:50,140 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 16:59:50,140 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 16:59:50,146 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_165950_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:59:50,155 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_165950_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:59:50,155 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_165950_Screenshot 2025-05-15 151122.jpg
2025-05-23 16:59:50,167 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-23 16:59:50,168 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (426, 333), format: JPEG
2025-05-23 16:59:50,170 - app.utils.face_utils - INFO - Image extrema values: ((8, 242), (1, 238), (0, 249))
2025-05-23 16:59:50,172 - app.utils.face_utils - INFO - Converted to array - Shape: (333, 426, 3), dtype: uint8
2025-05-23 16:59:50,174 - app.utils.face_utils - INFO - Final array shape: (333, 426, 3), dtype: uint8, range: [0, 249]
2025-05-23 16:59:50,174 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 16:59:50,183 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_165950_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 17:07:17,767 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:07:17,767 - backend.main - INFO - Application startup complete
2025-05-23 17:09:45,131 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-23 17:09:45,143 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_170945_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:09:45,144 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-23 17:09:45,144 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-23 17:09:45,144 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 17:09:45,146 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_170945_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:09:45,149 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_170945_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:09:45,149 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_170945_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:09:45,159 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-23 17:09:45,161 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (426, 333), format: JPEG
2025-05-23 17:09:45,162 - app.utils.face_utils - INFO - Image extrema values: ((8, 242), (1, 238), (0, 249))
2025-05-23 17:09:45,162 - app.utils.face_utils - INFO - Converted to array - Shape: (333, 426, 3), dtype: uint8
2025-05-23 17:09:45,163 - app.utils.face_utils - INFO - Final array shape: (333, 426, 3), dtype: uint8, range: [0, 249]
2025-05-23 17:09:45,163 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 17:09:45,166 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_170945_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 17:10:10,339 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.png
2025-05-23 17:10:10,339 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_171010_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:10:10,339 - app.utils.face_utils - INFO - Read 126761 bytes from uploaded file
2025-05-23 17:10:10,340 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (428, 380), Format: PNG
2025-05-23 17:10:10,340 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 17:10:10,342 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_171010_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:10:10,343 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_171010_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:10:10,344 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_171010_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:10:10,356 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (428, 380), Format: JPEG
2025-05-23 17:10:10,358 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (428, 380), format: JPEG
2025-05-23 17:10:10,359 - app.utils.face_utils - INFO - Image extrema values: ((20, 237), (7, 238), (3, 251))
2025-05-23 17:10:10,359 - app.utils.face_utils - INFO - Converted to array - Shape: (380, 428, 3), dtype: uint8
2025-05-23 17:10:10,359 - app.utils.face_utils - INFO - Final array shape: (380, 428, 3), dtype: uint8, range: [3, 251]
2025-05-23 17:10:10,360 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 17:10:10,360 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_171010_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 17:17:16,350 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:17:16,351 - backend.main - INFO - Application startup complete
2025-05-23 17:22:19,067 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.png
2025-05-23 17:22:19,080 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172219_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:22:19,081 - app.utils.face_utils - INFO - Read 126761 bytes from uploaded file
2025-05-23 17:22:19,081 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (428, 380), Format: PNG
2025-05-23 17:22:19,081 - app.utils.face_utils - INFO - Converting image from RGBA to RGB
2025-05-23 17:22:19,084 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172219_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:22:19,086 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172219_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:22:19,086 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172219_Screenshot 2025-05-15 151007.jpg
2025-05-23 17:22:19,097 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (428, 380), Format: JPEG
2025-05-23 17:22:19,097 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (428, 380), format: JPEG
2025-05-23 17:22:19,099 - app.utils.face_utils - INFO - Image extrema values: ((20, 237), (7, 238), (3, 251))
2025-05-23 17:22:19,100 - app.utils.face_utils - INFO - Converted to array - Shape: (380, 428, 3), dtype: uint8
2025-05-23 17:22:19,100 - app.utils.face_utils - INFO - Final array shape: (380, 428, 3), dtype: uint8, range: [3, 251]
2025-05-23 17:22:19,100 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 17:22:19,104 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172219_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 17:23:01,816 - app.utils.face_utils - INFO - Starting to process uploaded file: 20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:23:01,817 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172301_20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:23:01,817 - app.utils.face_utils - INFO - Read 26380 bytes from uploaded file
2025-05-23 17:23:01,817 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-23 17:23:01,818 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172301_20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:23:01,821 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172301_20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:23:01,821 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172301_20250523_160822_Screenshot 2025-05-15 151122.jpg
2025-05-23 17:23:01,830 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-23 17:23:01,830 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (426, 333), format: JPEG
2025-05-23 17:23:01,832 - app.utils.face_utils - INFO - Image extrema values: ((8, 243), (1, 237), (0, 249))
2025-05-23 17:23:01,832 - app.utils.face_utils - INFO - Converted to array - Shape: (333, 426, 3), dtype: uint8
2025-05-23 17:23:01,832 - app.utils.face_utils - INFO - Final array shape: (333, 426, 3), dtype: uint8, range: [0, 249]
2025-05-23 17:23:01,833 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-23 17:23:01,833 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250523_172301_20250523_160822_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-23 17:27:16,732 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:27:16,732 - backend.main - INFO - Application startup complete
2025-05-23 17:27:52,477 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:27:52,478 - backend.main - INFO - Application startup complete
2025-05-23 17:32:53,501 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:32:53,501 - backend.main - INFO - Application startup complete
2025-05-23 17:35:12,637 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:35:12,637 - backend.main - INFO - Application startup complete
2025-05-23 17:37:52,456 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:37:52,456 - backend.main - INFO - Application startup complete
2025-05-23 17:42:52,536 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:42:52,536 - backend.main - INFO - Application startup complete
2025-05-23 17:46:33,216 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 17:46:33,216 - backend.main - INFO - Application startup complete
2025-05-23 21:53:40,327 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-23 21:53:40,327 - backend.main - INFO - Application startup complete
2025-05-23 21:57:36,835 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\CODE\pythonProject\face_regenization\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 14:33:32,068 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-26 14:33:32,068 - backend.main - INFO - Application startup complete
2025-05-26 16:06:02,043 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\CODE\pythonProject\face_regenization\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 16:19:53,098 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-26 16:19:53,119 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250526_161953_Screenshot 2025-05-15 151007.jpg
2025-05-26 16:19:53,120 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-26 16:19:53,120 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-26 16:19:53,131 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250526_161953_Screenshot 2025-05-15 151007.jpg
2025-05-26 16:19:53,139 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250526_161953_Screenshot 2025-05-15 151007.jpg
2025-05-26 16:19:53,140 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250526_161953_Screenshot 2025-05-15 151007.jpg
2025-05-26 16:19:53,152 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-26 16:19:53,152 - app.utils.face_utils - INFO - Preprocessing image - Original mode: RGB, size: (856, 760), format: JPEG
2025-05-26 16:19:53,157 - app.utils.face_utils - INFO - Image extrema values: ((16, 255), (8, 248), (0, 255))
2025-05-26 16:19:53,170 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-26 16:19:53,176 - app.utils.face_utils - INFO - Final array shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-26 16:19:53,176 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-26 16:19:53,194 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250526_161953_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-27 08:37:43,693 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-27 08:37:43,693 - backend.main - INFO - Application startup complete
2025-05-27 16:25:15,640 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-27 16:25:15,698 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_162515_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:25:15,699 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-27 16:25:15,699 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-27 16:25:15,714 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_162515_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:25:15,724 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_162515_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:25:15,724 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_162515_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:25:15,737 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-27 16:25:15,737 - app.utils.face_utils - INFO - Starting enhanced image preprocessing - Mode: RGB, size: (856, 760), format: JPEG
2025-05-27 16:25:15,741 - app.utils.face_utils - INFO - Applied EXIF orientation correction
2025-05-27 16:25:15,744 - app.utils.face_utils - INFO - Image extrema values: ((16, 255), (8, 248), (0, 255))
2025-05-27 16:25:15,759 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-27 16:25:15,766 - app.utils.face_utils - INFO - Successfully preprocessed image - Final shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-27 16:25:15,767 - app.utils.face_utils - INFO - Preprocessed image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-27 16:25:15,767 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-27 16:25:15,790 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_162515_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-27 16:30:08,285 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-27 16:30:08,286 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_163008_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:30:08,286 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-27 16:30:08,286 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-27 16:30:08,290 - app.utils.face_utils - INFO - Saving image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_163008_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:30:08,296 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_163008_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:30:08,297 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_163008_Screenshot 2025-05-15 151007.jpg
2025-05-27 16:30:08,310 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-27 16:30:08,310 - app.utils.face_utils - INFO - Starting enhanced image preprocessing - Mode: RGB, size: (856, 760), format: JPEG
2025-05-27 16:30:08,314 - app.utils.face_utils - INFO - Applied EXIF orientation correction
2025-05-27 16:30:08,316 - app.utils.face_utils - INFO - Image extrema values: ((16, 255), (8, 248), (0, 255))
2025-05-27 16:30:08,318 - app.utils.face_utils - INFO - Converted to array - Shape: (760, 856, 3), dtype: uint8
2025-05-27 16:30:08,318 - app.utils.face_utils - INFO - Successfully preprocessed image - Final shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-27 16:30:08,318 - app.utils.face_utils - INFO - Preprocessed image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-27 16:30:08,319 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-27 16:30:08,319 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250527_163008_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 08:41:23,212 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 08:41:23,213 - backend.main - INFO - Application startup complete
2025-05-28 16:10:38,917 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 16:10:38,917 - backend.main - INFO - Application startup complete
2025-05-28 16:11:12,105 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 16:11:12,121 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg
2025-05-28 16:11:12,122 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 16:11:12,122 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 16:11:12,133 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg
2025-05-28 16:11:12,143 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg
2025-05-28 16:11:12,153 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 16:11:12,154 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg
2025-05-28 16:11:12,154 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 16:11:12,154 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 16:11:12,158 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 16:11:12,173 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 16:11:12,173 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-28 16:11:12,183 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 16:11:12,183 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 16:11:12,194 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 16:11:12,194 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_161112_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:02:00,159 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 17:02:00,160 - backend.main - INFO - Application startup complete
2025-05-28 17:02:54,680 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 17:02:54,691 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:02:54,692 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 17:02:54,692 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:02:54,700 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:02:54,706 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:02:54,717 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:02:54,717 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:02:54,718 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:02:54,718 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:02:54,722 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:02:54,736 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:02:54,737 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-28 17:02:54,744 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 17:02:54,744 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:02:54,750 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:02:54,751 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170254_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:03:31,558 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-28 17:03:31,564 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:03:31,565 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-28 17:03:31,565 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-28 17:03:31,565 - app.utils.face_utils - INFO - Converting RGBA to RGB with white background
2025-05-28 17:03:31,570 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:03:31,572 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:03:31,586 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (426, 333)
2025-05-28 17:03:31,586 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:03:31,586 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-28 17:03:31,586 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:03:31,587 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg_face_detection_temp.jpg
2025-05-28 17:03:31,600 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (426, 333)
2025-05-28 17:03:31,600 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-28 17:03:31,601 - app.utils.face_utils - INFO - Loaded image array - Shape: (333, 426, 3), dtype: uint8, range: [0, 249]
2025-05-28 17:03:31,602 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:03:31,602 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:03:31,602 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170331_Screenshot 2025-05-15 151122.jpg_face_detection_temp.jpg
2025-05-28 17:06:40,227 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151122.png
2025-05-28 17:06:40,227 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:06:40,227 - app.utils.face_utils - INFO - Read 120720 bytes from uploaded file
2025-05-28 17:06:40,227 - app.utils.face_utils - INFO - Original image info - Mode: RGBA, Size: (426, 333), Format: PNG
2025-05-28 17:06:40,228 - app.utils.face_utils - INFO - Converting RGBA to RGB with white background
2025-05-28 17:06:40,231 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:06:40,234 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:06:40,244 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (426, 333)
2025-05-28 17:06:40,244 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg
2025-05-28 17:06:40,244 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (426, 333), Format: JPEG
2025-05-28 17:06:40,244 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:06:40,246 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg_face_detection_temp.jpg
2025-05-28 17:06:40,258 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (426, 333)
2025-05-28 17:06:40,259 - app.utils.face_utils - INFO - Loading image with face_recognition...
2025-05-28 17:06:40,261 - app.utils.face_utils - INFO - Loaded image array - Shape: (333, 426, 3), dtype: uint8, range: [0, 249]
2025-05-28 17:06:40,261 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:06:40,261 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:06:40,262 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170640_Screenshot 2025-05-15 151122.jpg_face_detection_temp.jpg
2025-05-28 17:06:58,930 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 17:06:58,930 - backend.main - INFO - Application startup complete
2025-05-28 17:06:59,943 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 17:06:59,953 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:06:59,954 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 17:06:59,954 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:06:59,957 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:06:59,961 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:06:59,971 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:06:59,971 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:06:59,972 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:06:59,972 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:06:59,974 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:06:59,989 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:06:59,989 - app.utils.face_utils - INFO - Loading image with PIL...
2025-05-28 17:06:59,993 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 17:06:59,993 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:06:59,998 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:06:59,998 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_170659_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:11:58,963 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 17:11:58,964 - backend.main - INFO - Application startup complete
2025-05-28 17:56:30,035 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 17:56:30,072 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:56:30,073 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 17:56:30,073 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:56:30,088 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:56:30,097 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:56:30,112 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:56:30,113 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:56:30,113 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:56:30,113 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:56:30,118 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:56:30,136 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:56:30,137 - app.utils.face_utils - INFO - Loading image with OpenCV...
2025-05-28 17:56:30,209 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 17:56:30,209 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:56:30,227 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:56:30,228 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175630_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:56:55,062 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 17:56:55,062 - backend.main - INFO - Application startup complete
2025-05-28 17:57:23,522 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 17:57:23,533 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:57:23,533 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 17:57:23,535 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:57:23,537 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:57:23,542 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:57:23,559 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:57:23,560 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg
2025-05-28 17:57:23,560 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 17:57:23,560 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 17:57:23,564 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 17:57:23,582 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 17:57:23,583 - app.utils.face_utils - INFO - Loading image with OpenCV...
2025-05-28 17:57:23,591 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 17:57:23,591 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 17:57:23,591 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 17:57:23,591 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_175723_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 18:01:55,337 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 18:01:55,337 - backend.main - INFO - Application startup complete
2025-05-28 18:02:15,202 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 18:02:15,220 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:02:15,222 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 18:02:15,222 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 18:02:15,226 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:02:15,233 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:02:15,244 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 18:02:15,245 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:02:15,245 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 18:02:15,245 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 18:02:15,249 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 18:02:15,268 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 18:02:15,268 - app.utils.face_utils - INFO - Loading image with OpenCV...
2025-05-28 18:02:15,277 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 18:02:15,277 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 18:02:15,277 - app.utils.face_utils - INFO - Array details before face_recognition call:
2025-05-28 18:02:15,277 - app.utils.face_utils - INFO -   - Shape: (760, 856, 3)
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - Dtype: uint8
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - Min/Max: [0, 255]
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - C_CONTIGUOUS: True
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - F_CONTIGUOUS: False
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - OWNDATA: True
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO -   - WRITEABLE: True
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO - Attempting method 1: Direct call...
2025-05-28 18:02:15,279 - app.utils.face_utils - WARNING - Method 1 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:02:15,279 - app.utils.face_utils - INFO - Attempting method 2: Creating new contiguous array...
2025-05-28 18:02:15,280 - app.utils.face_utils - INFO - New array - Shape: (760, 856, 3), dtype: uint8, C_CONTIGUOUS: True
2025-05-28 18:02:15,280 - app.utils.face_utils - WARNING - Method 2 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:02:15,280 - app.utils.face_utils - INFO - Attempting method 3: Using array copy...
2025-05-28 18:02:15,281 - app.utils.face_utils - INFO - Copied array - Shape: (760, 856, 3), dtype: uint8, C_CONTIGUOUS: True
2025-05-28 18:02:15,281 - app.utils.face_utils - WARNING - Method 3 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:02:15,281 - app.utils.face_utils - INFO - Attempting method 4: Using face_recognition.load_image_file...
2025-05-28 18:02:15,296 - app.utils.face_utils - INFO - FR loaded image - Shape: (760, 856, 3), dtype: uint8
2025-05-28 18:02:15,296 - app.utils.face_utils - ERROR - All methods failed. Last error: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:02:15,296 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:02:15,297 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180215_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 18:06:54,417 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 18:06:54,417 - backend.main - INFO - Application startup complete
2025-05-28 18:07:00,451 - app.utils.face_utils - INFO - Starting to process uploaded file: Screenshot 2025-05-15 151007.jpg
2025-05-28 18:07:00,466 - app.utils.face_utils - INFO - Target file path: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:07:00,466 - app.utils.face_utils - INFO - Read 130075 bytes from uploaded file
2025-05-28 18:07:00,467 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 18:07:00,471 - app.utils.face_utils - INFO - Saving processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:07:00,478 - app.utils.face_utils - INFO - Successfully saved processed image to D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:07:00,489 - app.utils.face_utils - INFO - Saved image verified - Mode: RGB, Size: (856, 760)
2025-05-28 18:07:00,490 - app.utils.face_utils - INFO - Starting face detection for image: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg
2025-05-28 18:07:00,491 - app.utils.face_utils - INFO - Original image info - Mode: RGB, Size: (856, 760), Format: JPEG
2025-05-28 18:07:00,491 - app.utils.face_utils - INFO - Converting image to standard RGB format...
2025-05-28 18:07:00,495 - app.utils.face_utils - INFO - Saving processed image to temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 18:07:00,513 - app.utils.face_utils - INFO - Temp image verified - Mode: RGB, Size: (856, 760)
2025-05-28 18:07:00,513 - app.utils.face_utils - INFO - Loading image with OpenCV...
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO - Loaded image array - Shape: (760, 856, 3), dtype: uint8, range: [0, 255]
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO - Detecting face locations...
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO - Array details before face_recognition call:
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - Shape: (760, 856, 3)
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - Dtype: uint8
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - Min/Max: [0, 255]
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - C_CONTIGUOUS: True
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - F_CONTIGUOUS: False
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - OWNDATA: True
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO -   - WRITEABLE: True
2025-05-28 18:07:00,522 - app.utils.face_utils - INFO - Attempting method 1: Direct call...
2025-05-28 18:07:00,523 - app.utils.face_utils - WARNING - Method 1 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,523 - app.utils.face_utils - INFO - Attempting method 2: Creating new contiguous array...
2025-05-28 18:07:00,523 - app.utils.face_utils - INFO - New array - Shape: (760, 856, 3), dtype: uint8, C_CONTIGUOUS: True
2025-05-28 18:07:00,523 - app.utils.face_utils - WARNING - Method 2 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,523 - app.utils.face_utils - INFO - Attempting method 3: Using array copy...
2025-05-28 18:07:00,524 - app.utils.face_utils - INFO - Copied array - Shape: (760, 856, 3), dtype: uint8, C_CONTIGUOUS: True
2025-05-28 18:07:00,524 - app.utils.face_utils - WARNING - Method 3 failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,524 - app.utils.face_utils - INFO - Attempting method 4: Using face_recognition.load_image_file...
2025-05-28 18:07:00,531 - app.utils.face_utils - INFO - FR loaded image - Shape: (760, 856, 3), dtype: uint8
2025-05-28 18:07:00,531 - app.utils.face_utils - ERROR - All methods failed. Last error: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,531 - app.utils.face_utils - INFO - Attempting final test: Creating a simple test image...
2025-05-28 18:07:00,532 - app.utils.face_utils - INFO - Test image - Shape: (100, 100, 3), dtype: uint8
2025-05-28 18:07:00,532 - app.utils.face_utils - ERROR - Even test image failed: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,532 - app.utils.face_utils - ERROR - This suggests a fundamental issue with the face_recognition library installation.
2025-05-28 18:07:00,532 - app.utils.face_utils - ERROR - Error detecting faces in D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg: Unsupported image type, must be 8bit gray or RGB image.
2025-05-28 18:07:00,533 - app.utils.face_utils - INFO - Cleaned up temporary file: D:\CODE\pythonProject\face_regenization\backend\uploads\known\20250528_180700_Screenshot 2025-05-15 151007.jpg_face_detection_temp.jpg
2025-05-28 18:14:17,356 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 18:14:17,357 - backend.main - INFO - Application startup complete
2025-05-28 18:15:11,036 - app.db.init_db - INFO - Admin user already exists: admin
2025-05-28 18:15:11,037 - main - INFO - Application startup complete
