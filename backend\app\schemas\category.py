from typing import List, Optional
from datetime import datetime

from pydantic import BaseModel


# 共享属性
class CategoryBase(BaseModel):
    """
    分类基础模式
    """
    name: str
    description: Optional[str] = None


class CategoryCreate(CategoryBase):
    """
    分类创建模式
    """
    pass


class CategoryUpdate(CategoryBase):
    """
    分类更新模式
    """
    name: Optional[str] = None


class CategoryInDBBase(CategoryBase):
    """
    数据库中的分类基础模式
    """
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


class Category(CategoryInDBBase):
    """
    分类模式（返回给API的）
    """
    pass


# 批量操作模式
class FaceCategoryBulkUpdate(BaseModel):
    """
    批量更新人脸分类
    """
    face_ids: List[int]
    category_ids: List[int]
