from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel


# 共享属性
class AliasBase(BaseModel):
    """
    别名基础模式
    """
    name: str
    description: Optional[str] = None


class AliasCreate(AliasBase):
    """
    别名创建模式
    """
    pass


class AliasUpdate(AliasBase):
    """
    别名更新模式
    """
    name: Optional[str] = None


class AliasInDBBase(AliasBase):
    """
    数据库中的别名基础模式
    """
    id: int
    face_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class Alias(AliasInDBBase):
    """
    别名模式（返回给API的）
    """
    pass


# 批量操作模式
class AliasBulkCreate(BaseModel):
    """
    批量创建别名
    """
    face_id: int
    aliases: List[AliasCreate]


class AliasBulkDelete(BaseModel):
    """
    批量删除别名
    """
    alias_ids: List[int]
