/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} - 是否有效
 */
export const isValidEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

/**
 * 验证用户名格式
 * @param {string} username - 用户名
 * @returns {boolean} - 是否有效
 */
export const isValidUsername = (username) => {
  // 用户名长度在3-20之间，只能包含字母、数字、下划线
  const re = /^[a-zA-Z0-9_]{3,20}$/;
  return re.test(username);
};

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {object} - 包含是否有效和强度级别
 */
export const validatePassword = (password) => {
  if (!password || password.length < 6) {
    return { valid: false, strength: 'weak', message: '密码长度至少为6位' };
  }
  
  let strength = 'weak';
  let message = '弱密码';
  
  // 检查密码强度
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasDigit = /\d/.test(password);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  if (hasLetter && hasDigit && hasSpecial && password.length >= 8) {
    strength = 'strong';
    message = '强密码';
  } else if ((hasLetter && hasDigit) || (hasLetter && hasSpecial) || (hasDigit && hasSpecial)) {
    strength = 'medium';
    message = '中等强度密码';
  }
  
  return { valid: true, strength, message };
};

/**
 * 验证图片文件
 * @param {File} file - 文件对象
 * @returns {object} - 包含是否有效和错误信息
 */
export const validateImageFile = (file) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, message: '只支持JPG/JPEG和PNG格式的图片' };
  }
  
  // 检查文件大小（最大10MB）
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, message: '图片大小不能超过10MB' };
  }
  
  return { valid: true, message: '' };
};
