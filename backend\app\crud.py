from sqlalchemy.orm import Session
import os
from typing import List, Optional, Tuple
import numpy as np
from passlib.context import Crypt<PERSON>ontext
from . import models, schemas
from .face_utils import serialize_encoding, deserialize_encoding, compare_faces

# 密码哈希
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 用户相关操作
def get_user(db: Session, user_id: int):
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_email(db: Session, email: str):
    return db.query(models.User).filter(models.User.email == email).first()

def get_user_by_username(db: Session, username: str):
    return db.query(models.User).filter(models.User.username == username).first()

def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.User).offset(skip).limit(limit).all()

def create_user(db: Session, user: schemas.UserCreate, is_admin: int = 0):
    hashed_password = pwd_context.hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        is_admin=is_admin
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

# 人脸相关操作
def create_face(db: Session, face: schemas.FaceCreate, image_path: str, face_encoding: np.ndarray, user_id: int):
    # 序列化人脸编码
    encoding_binary = serialize_encoding(face_encoding)
    
    db_face = models.Face(
        name=face.name,
        image_path=image_path,
        face_encoding=encoding_binary,
        user_id=user_id
    )
    db.add(db_face)
    db.commit()
    db.refresh(db_face)
    return db_face

def get_face(db: Session, face_id: int):
    return db.query(models.Face).filter(models.Face.id == face_id).first()

def get_faces(db: Session, skip: int = 0, limit: int = 100, user_id: Optional[int] = None):
    query = db.query(models.Face)
    if user_id is not None:
        query = query.filter(models.Face.user_id == user_id)
    return query.offset(skip).limit(limit).all()

def update_face(db: Session, face_id: int, face: schemas.FaceCreate):
    db_face = db.query(models.Face).filter(models.Face.id == face_id).first()
    if db_face:
        db_face.name = face.name
        db.commit()
        db.refresh(db_face)
    return db_face

def delete_face(db: Session, face_id: int):
    db_face = db.query(models.Face).filter(models.Face.id == face_id).first()
    if db_face:
        # 删除图片文件
        if os.path.exists(db_face.image_path):
            os.remove(db_face.image_path)
        
        # 删除数据库记录
        db.delete(db_face)
        db.commit()
        return True
    return False

# 搜索相关操作
def create_search_history(db: Session, image_path: str, user_id: Optional[int] = None):
    db_search = models.SearchHistory(
        image_path=image_path,
        user_id=user_id
    )
    db.add(db_search)
    db.commit()
    db.refresh(db_search)
    return db_search

def create_search_result(db: Session, search_id: int, face_id: int, similarity: float):
    db_result = models.SearchResult(
        search_id=search_id,
        face_id=face_id,
        similarity=similarity
    )
    db.add(db_result)
    db.commit()
    db.refresh(db_result)
    return db_result

def get_search_history(db: Session, search_id: int):
    return db.query(models.SearchHistory).filter(models.SearchHistory.id == search_id).first()

def get_search_histories(db: Session, skip: int = 0, limit: int = 100, user_id: Optional[int] = None):
    query = db.query(models.SearchHistory)
    if user_id is not None:
        query = query.filter(models.SearchHistory.user_id == user_id)
    return query.order_by(models.SearchHistory.created_at.desc()).offset(skip).limit(limit).all()

def search_faces(db: Session, unknown_encoding: np.ndarray, threshold: float = 0.6) -> List[Tuple[models.Face, float]]:
    """搜索匹配的人脸"""
    results = []
    
    # 获取所有已知人脸
    faces = db.query(models.Face).all()
    
    # 比较每个已知人脸
    for face in faces:
        # 反序列化人脸编码
        known_encoding = deserialize_encoding(face.face_encoding)
        
        # 比较人脸
        _, similarity = compare_faces(known_encoding, unknown_encoding)
        
        # 如果相似度超过阈值，添加到结果中
        if similarity >= threshold:
            results.append((face, similarity))
    
    # 按相似度降序排序
    results.sort(key=lambda x: x[1], reverse=True)
    
    return results
