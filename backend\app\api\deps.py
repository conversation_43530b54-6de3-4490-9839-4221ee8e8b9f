from typing import Generator

from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2Password<PERSON>earer
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services.auth import (
    get_current_user,
    get_current_active_user,
    get_current_admin_user
)
from app.models.user import User

# 重新导出依赖项，以便在API端点中使用
get_db_session = get_db
get_current_user_dependency = get_current_user
get_current_active_user_dependency = get_current_active_user
get_current_admin_user_dependency = get_current_admin_user
