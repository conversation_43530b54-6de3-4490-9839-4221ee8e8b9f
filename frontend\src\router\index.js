import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '../store/modules/auth';

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('../views/search/FaceSearch.vue'),
    meta: { title: '人脸搜索' }
  },
  {
    path: '/management',
    name: 'Management',
    component: () => import('../views/face/FaceManagement.vue'),
    meta: { requiresAuth: true, title: '人脸管理' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/auth/UserProfile.vue'),
    meta: { requiresAuth: true, title: '个人资料' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '人脸识别搜索系统'}`;
  
  // 检查是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const authStore = useAuthStore();

    if (!authStore.isLoggedIn) {
      // 如果需要认证但未登录，保存目标路由并重定向到首页
      authStore.setRedirectPath(to.fullPath);
      ElMessage.warning('请先登录');
      next({ path: '/' });
    } else {
      // 检查是否需要管理员权限
      if (to.meta.requiresAdmin && !authStore.isAdmin) {
        ElMessage.error('无权访问该页面');
        next({ path: '/' });
      } else {
        next();
      }
    }
  } else {
    next();
  }
    
    if (!authStore.isLoggedIn) {
      // 如果需要认证但未登录，重定向到首页
      next({ path: '/' });
    } else {
      // 检查是否需要管理员权限
      if (to.matched.some(record => record.meta.requiresAdmin) && !authStore.isAdmin) {
        next({ path: '/' });
      } else {
        next();
      }
    }
  } else {
    next();
  }
});

export default router;
