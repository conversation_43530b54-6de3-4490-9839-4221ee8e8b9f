/**
 * 格式化日期时间
 * @param {string|Date} date - 日期对象或日期字符串
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} - 格式化后的日期字符串
 */
export const formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '';
  
  const d = new Date(date);
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 格式化相似度为百分比
 * @param {number} similarity - 相似度值（0-1之间）
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} - 格式化后的百分比字符串
 */
export const formatSimilarity = (similarity, decimals = 2) => {
  if (similarity === undefined || similarity === null) return '';
  
  return `${(similarity * 100).toFixed(decimals)}%`;
};

/**
 * 获取相似度的CSS类名
 * @param {number} similarity - 相似度值（0-1之间）
 * @returns {string} - CSS类名
 */
export const getSimilarityClass = (similarity) => {
  if (similarity >= 0.8) return 'similarity-high';
  if (similarity >= 0.6) return 'similarity-medium';
  return 'similarity-low';
};

/**
 * 获取图片URL
 * @param {string} path - 图片路径
 * @param {string} type - 图片类型，'known'或'unknown'
 * @returns {string} - 完整的图片URL
 */
export const getImageUrl = (path, type = 'known') => {
  if (!path) return '';
  
  // 从路径中提取文件名
  const filename = path.split('\\').pop().split('/').pop();
  
  // 构建URL
  const baseUrl = import.meta.env.VITE_API_BASE_URL || '/api/v1';
  return `${baseUrl}/uploads/${type}/${filename}`;
};
