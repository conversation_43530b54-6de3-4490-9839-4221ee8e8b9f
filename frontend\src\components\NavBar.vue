<template>
  <header class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <router-link to="/">人脸识别搜索系统</router-link>
      </div>
      <nav class="nav-links">
        <router-link to="/" class="nav-link">首页</router-link>
        <router-link to="/search" class="nav-link">人脸搜索</router-link>
        <router-link to="/management" class="nav-link" v-if="isLoggedIn">人脸管理</router-link>
      </nav>
      <div class="user-actions">
        <template v-if="isLoggedIn">
          <el-dropdown>
            <span class="user-info">
              {{ user?.username }}
              <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="primary" size="small" @click="showLoginModal = true">登录</el-button>
          <el-button size="small" @click="showRegisterModal = true">注册</el-button>
        </template>
      </div>
    </div>

    <!-- 登录对话框 -->
    <el-dialog v-model="showLoginModal" title="登录" width="30%" destroy-on-close>
      <el-form :model="loginForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" 
            @keyup.enter="login"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showLoginModal = false">取消</el-button>
          <el-button type="primary" @click="login">登录</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 注册对话框 -->
    <el-dialog v-model="showRegisterModal" title="注册" width="30%" destroy-on-close>
      <el-form :model="registerForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"
            @keyup.enter="register"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRegisterModal = false">取消</el-button>
          <el-button type="primary" @click="register">注册</el-button>
        </span>
      </template>
    </el-dialog>
  </header>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElButton, ElInput, ElForm, ElFormItem, ElDialog, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon } from 'element-plus';
import { ArrowDown } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../store/modules/auth';
import { login as authLogin, getCurrentUser, register as authRegister } from '../api/auth';

const router = useRouter();
const authStore = useAuthStore();

// 状态
const isLoggedIn = computed(() => authStore.isLoggedIn);
const user = computed(() => authStore.user);
const showLoginModal = ref(false);
const showRegisterModal = ref(false);
const loginForm = ref({
  username: '',
  password: ''
});
const registerForm = ref({
  username: '',
  email: '',
  password: ''
});

// 检查是否已登录
const checkAuth = async () => {
  if (authStore.isLoggedIn) {
    try {
      const userData = await getCurrentUser();
      authStore.setUser(userData);
    } catch (error) {
      console.error('Auth check failed:', error);
      authStore.logout();
      router.push('/');
    }
  }
};

// 初始化时检查认证状态
onMounted(() => {
  checkAuth();
});

// 登录
const login = async () => {
  try {
    const result = await authLogin({
      username: loginForm.value.username,
      password: loginForm.value.password
    });
    
    if (result.access_token) {
      authStore.setToken(result.access_token);
      showLoginModal.value = false;
      await checkAuth();
      ElMessage.success('登录成功');
      loginForm.value = { username: '', password: '' }; // 清空表单
      // 获取重定向路径并导航
      const redirectPath = authStore.getRedirectPath();
      router.push(redirectPath);
    } else {
      throw new Error('未收到访问令牌');
    }
  } catch (error) {
    console.error('Login failed:', error);
    ElMessage.error('登录失败: ' + (error.response?.data?.detail || '未知错误'));
  }
};

// 注册
const register = async () => {
  try {
    await authRegister({
      username: registerForm.value.username,
      email: registerForm.value.email,
      password: registerForm.value.password
    });
    showRegisterModal.value = false;
    ElMessage.success('注册成功，请登录');
    registerForm.value = { username: '', email: '', password: '' }; // 清空表单
    showLoginModal.value = true;
  } catch (error) {
    console.error('Registration failed:', error);
    ElMessage.error('注册失败: ' + (error.response?.data?.detail || '未知错误'));
  }
};

// 退出登录
const logout = () => {
  authStore.logout();
  router.push('/');
  ElMessage.success('已退出登录');
};
</script>

<style scoped>
.navbar {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo a {
  font-size: 1.5rem;
  font-weight: bold;
  color: #409eff;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #606266;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #409eff;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #409eff;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
</style>
