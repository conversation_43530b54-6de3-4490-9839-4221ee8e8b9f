import apiClient from './index';

/**
 * 获取分类列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的最大记录数
 * @returns {Promise} - 返回分类列表
 */
export const getCategories = async (params = {}) => {
  const response = await apiClient.get('/categories/', { params });
  return response.data;
};

/**
 * 获取单个分类
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回分类信息
 */
export const getCategory = async (categoryId) => {
  const response = await apiClient.get(`/categories/${categoryId}`);
  return response.data;
};

/**
 * 创建新分类
 * @param {Object} categoryData - 分类数据
 * @param {string} categoryData.name - 分类名称
 * @param {string} categoryData.description - 分类描述（可选）
 * @returns {Promise} - 返回创建的分类信息
 */
export const createCategory = async (categoryData) => {
  const response = await apiClient.post('/categories/', categoryData);
  return response.data;
};

/**
 * 更新分类信息
 * @param {number} categoryId - 分类ID
 * @param {Object} categoryData - 分类数据
 * @param {string} categoryData.name - 分类名称
 * @param {string} categoryData.description - 分类描述
 * @returns {Promise} - 返回更新后的分类信息
 */
export const updateCategory = async (categoryId, categoryData) => {
  const response = await apiClient.put(`/categories/${categoryId}`, categoryData);
  return response.data;
};

/**
 * 删除分类
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回删除结果
 */
export const deleteCategory = async (categoryId) => {
  const response = await apiClient.delete(`/categories/${categoryId}`);
  return response.data;
};

/**
 * 将人脸添加到分类
 * @param {number} categoryId - 分类ID
 * @param {Array<number>} faceIds - 人脸ID列表
 * @returns {Promise} - 返回添加结果
 */
export const addFacesToCategory = async (categoryId, faceIds) => {
  const response = await apiClient.post(`/categories/${categoryId}/faces`, faceIds);
  return response.data;
};

/**
 * 从分类中移除人脸
 * @param {number} categoryId - 分类ID
 * @param {Array<number>} faceIds - 人脸ID列表
 * @returns {Promise} - 返回移除结果
 */
export const removeFacesFromCategory = async (categoryId, faceIds) => {
  const response = await apiClient.delete(`/categories/${categoryId}/faces`, { data: faceIds });
  return response.data;
};

/**
 * 批量更新人脸分类
 * @param {Object} data - 更新数据
 * @param {Array<number>} data.face_ids - 人脸ID列表
 * @param {Array<number>} data.category_ids - 分类ID列表
 * @returns {Promise} - 返回更新结果
 */
export const bulkUpdateCategories = async (data) => {
  const response = await apiClient.post('/categories/bulk-update', data);
  return response.data;
};
