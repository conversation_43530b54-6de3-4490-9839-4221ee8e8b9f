import os
import logging
from typing import List, Optional, Dict, Any

import numpy as np
from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.face import Face
from app.models.user import User
from app.schemas.face import FaceCreate, FaceUpdate
from app.utils.face_utils import (
    save_uploaded_image, 
    detect_faces, 
    serialize_encoding,
    deserialize_encoding
)

# 设置日志记录器
logger = logging.getLogger(__name__)


def get_face(db: Session, face_id: int) -> Optional[Face]:
    """
    通过ID获取人脸
    
    Args:
        db: 数据库会话
        face_id: 人脸ID
        
    Returns:
        Optional[Face]: 人脸对象，如果不存在则返回None
    """
    return db.query(Face).filter(Face.id == face_id).first()


def get_faces(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    user_id: Optional[int] = None
) -> List[Face]:
    """
    获取人脸列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的最大记录数
        user_id: 用户ID（可选，用于筛选特定用户的人脸）
        
    Returns:
        List[Face]: 人脸列表
    """
    query = db.query(Face)
    if user_id is not None:
        query = query.filter(Face.user_id == user_id)
    return query.offset(skip).limit(limit).all()


def create_face(
    db: Session, 
    file: UploadFile, 
    face_data: FaceCreate, 
    user_id: int
) -> Face:
    """
    创建新人脸
    
    Args:
        db: 数据库会话
        file: 上传的图片文件
        face_data: 人脸创建模式
        user_id: 用户ID
        
    Returns:
        Face: 创建的人脸对象
    """
    # 保存上传的图片
    image_path = save_uploaded_image(file, settings.KNOWN_FACES_DIR)
    
    # 检测人脸
    faces = detect_faces(image_path)
    
    # 使用第一个检测到的人脸
    face_info = faces[0]
    face_encoding = face_info["encoding"]
    
    # 序列化人脸编码
    encoding_binary = serialize_encoding(face_encoding)
    
    # 创建人脸记录
    db_face = Face(
        name=face_data.name,
        description=face_data.description,
        image_path=image_path,
        face_encoding=encoding_binary,
        user_id=user_id
    )
    
    db.add(db_face)
    db.commit()
    db.refresh(db_face)
    
    logger.info(f"Created face: {db_face.id} - {db_face.name}")
    return db_face


def update_face(
    db: Session, 
    face_id: int, 
    face_data: FaceUpdate
) -> Optional[Face]:
    """
    更新人脸信息
    
    Args:
        db: 数据库会话
        face_id: 人脸ID
        face_data: 人脸更新模式
        
    Returns:
        Optional[Face]: 更新后的人脸对象，如果人脸不存在则返回None
    """
    db_face = get_face(db, face_id=face_id)
    if not db_face:
        return None
    
    update_data = face_data.dict(exclude_unset=True)
    
    # 更新人脸属性
    for field, value in update_data.items():
        if hasattr(db_face, field):
            setattr(db_face, field, value)
    
    db.commit()
    db.refresh(db_face)
    
    logger.info(f"Updated face: {db_face.id} - {db_face.name}")
    return db_face


def delete_face(db: Session, face_id: int) -> bool:
    """
    删除人脸
    
    Args:
        db: 数据库会话
        face_id: 人脸ID
        
    Returns:
        bool: 是否成功删除
    """
    db_face = get_face(db, face_id=face_id)
    if not db_face:
        return False
    
    # 删除图片文件
    if os.path.exists(db_face.image_path):
        os.remove(db_face.image_path)
    
    # 删除数据库记录
    db.delete(db_face)
    db.commit()
    
    logger.info(f"Deleted face: {face_id}")
    return True


def check_face_owner(db: Session, face_id: int, user_id: int) -> bool:
    """
    检查人脸是否属于指定用户
    
    Args:
        db: 数据库会话
        face_id: 人脸ID
        user_id: 用户ID
        
    Returns:
        bool: 人脸是否属于指定用户
    """
    db_face = get_face(db, face_id=face_id)
    if not db_face:
        return False
    
    return db_face.user_id == user_id
