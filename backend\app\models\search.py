from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class SearchHistory(Base):
    """
    搜索历史数据模型
    """
    # 基本信息
    image_path = Column(String, nullable=False)  # 搜索图片存储路径
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("user.id"), nullable=True)  # 可以为匿名用户
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User", back_populates="search_histories")
    results = relationship("SearchResult", back_populates="search", cascade="all, delete-orphan")


class SearchResult(Base):
    """
    搜索结果数据模型
    """
    # 关联信息
    search_id = Column(Integer, ForeignKey("searchhistory.id"), nullable=False)
    face_id = Column(Integer, Foreign<PERSON>ey("face.id"), nullable=False)
    
    # 结果信息
    similarity = Column(Float, nullable=False)  # 相似度得分
    
    # 关系
    search = relationship("SearchHistory", back_populates="results")
    face = relationship("Face", back_populates="search_results")
