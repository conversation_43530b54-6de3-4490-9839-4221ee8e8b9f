from sqlalchemy import Column, Integer, String, LargeBinary, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base
from app.models.category import face_category


class Face(Base):
    """
    人脸数据模型
    """
    # 基本信息
    name = Column(String, index=True, nullable=False)  # 人物姓名
    description = Column(String, nullable=True)  # 人物描述

    # 文件信息
    image_path = Column(String, nullable=False)  # 图片存储路径
    face_encoding = Column(LargeBinary, nullable=False)  # 人脸特征向量，使用pickle序列化的numpy数组

    # 关联信息
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    owner = relationship("User", back_populates="faces")
    search_results = relationship("SearchResult", back_populates="face", cascade="all, delete-orphan")
    categories = relationship("Category", secondary=face_category, back_populates="faces")
