from typing import List, Optional
from datetime import datetime

from pydantic import BaseModel

from app.schemas.face import Face


# 搜索结果模式
class SearchResultBase(BaseModel):
    """
    搜索结果基础模式
    """
    face_id: int
    similarity: float


class SearchResultCreate(SearchResultBase):
    """
    搜索结果创建模式
    """
    search_id: int


class SearchResult(SearchResultBase):
    """
    搜索结果模式（返回给API的）
    """
    id: int
    face: Face

    class Config:
        from_attributes = True


# 搜索历史模式
class SearchHistoryBase(BaseModel):
    """
    搜索历史基础模式
    """
    user_id: Optional[int] = None


class SearchHistoryCreate(SearchHistoryBase):
    """
    搜索历史创建模式
    """
    image_path: str


class SearchHistory(SearchHistoryBase):
    """
    搜索历史模式（返回给API的）
    """
    id: int
    image_path: str
    created_at: datetime
    results: List[SearchResult] = []

    class Config:
        from_attributes = True


# 搜索请求模式
class SearchRequest(BaseModel):
    """
    搜索请求模式
    """
    threshold: Optional[float] = 0.6
