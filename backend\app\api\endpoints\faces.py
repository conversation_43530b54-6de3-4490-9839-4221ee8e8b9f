from typing import Any, List

from fastapi import APIRouter, Depends, File, Form, UploadFile
from sqlalchemy.orm import Session

from app.api.deps import (
    get_db_session, 
    get_current_active_user_dependency,
    get_current_admin_user_dependency
)
from app.core.exceptions import NotFoundException, ForbiddenException
from app.models.user import User
from app.schemas.face import Face, FaceCreate, FaceUpdate
from app.services.face import (
    get_face, 
    get_faces, 
    create_face, 
    update_face, 
    delete_face,
    check_face_owner
)

router = APIRouter()


@router.get("/", response_model=List[Face])
def read_faces(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取人脸列表
    
    普通用户只能看到自己的人脸，管理员可以看到所有人脸
    """
    # 普通用户只能看到自己的人脸，管理员可以看到所有人脸
    user_id = None if current_user.is_admin == 1 else current_user.id
    faces = get_faces(db, skip=skip, limit=limit, user_id=user_id)
    return faces


@router.post("/", response_model=Face)
def create_new_face(
    name: str = Form(...),
    description: str = Form(None),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    创建新人脸
    """
    face_data = FaceCreate(name=name, description=description)
    return create_face(db=db, file=file, face_data=face_data, user_id=current_user.id)


@router.get("/{face_id}", response_model=Face)
def read_face(
    face_id: int,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    获取特定人脸
    
    普通用户只能看到自己的人脸，管理员可以看到所有人脸
    """
    face = get_face(db, face_id=face_id)
    if not face:
        raise NotFoundException("Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and face.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    return face


@router.put("/{face_id}", response_model=Face)
def update_face_info(
    face_id: int,
    face_data: FaceUpdate,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    更新人脸信息
    
    普通用户只能更新自己的人脸，管理员可以更新所有人脸
    """
    face = get_face(db, face_id=face_id)
    if not face:
        raise NotFoundException("Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and face.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    updated_face = update_face(db=db, face_id=face_id, face_data=face_data)
    return updated_face


@router.delete("/{face_id}")
def delete_face_record(
    face_id: int,
    current_user: User = Depends(get_current_active_user_dependency),
    db: Session = Depends(get_db_session)
) -> Any:
    """
    删除人脸
    
    普通用户只能删除自己的人脸，管理员可以删除所有人脸
    """
    face = get_face(db, face_id=face_id)
    if not face:
        raise NotFoundException("Face not found")
    
    # 检查权限
    if current_user.is_admin != 1 and face.user_id != current_user.id:
        raise ForbiddenException("Not enough permissions")
    
    success = delete_face(db=db, face_id=face_id)
    if not success:
        raise NotFoundException("Face not found")
    
    return {"detail": "Face deleted successfully"}
