import { defineStore } from 'pinia';

export const useSearchStore = defineStore('search', {
  state: () => ({
    searchHistories: [],
    currentSearch: null,
  }),
  
  getters: {
    totalHistories: (state) => state.searchHistories.length,
  },
  
  actions: {
    setSearchHistories(histories) {
      this.searchHistories = histories;
    },
    
    setCurrentSearch(search) {
      this.currentSearch = search;
    },
    
    addSearchHistory(search) {
      this.searchHistories.unshift(search);
    },
  },
});
