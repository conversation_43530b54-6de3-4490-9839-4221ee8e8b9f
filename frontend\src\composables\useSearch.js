import { ref, computed } from 'vue';
import { useSearchStore } from '../store/modules/search';
import { searchFace, getSearchHistories, getSearchHistory } from '../api/search';
import { ElMessage } from 'element-plus';
import { getImageUrl } from '../utils/format';

export function useSearch() {
  const searchStore = useSearchStore();
  
  const loading = ref(false);
  const error = ref(null);
  const searchResults = ref([]);
  const previewImage = ref('');
  const selectedFile = ref(null);
  const searched = ref(false);
  
  // 计算属性
  const searchHistories = computed(() => searchStore.searchHistories);
  const currentSearch = computed(() => searchStore.currentSearch);
  
  // 处理文件选择
  const handleFileChange = (file) => {
    selectedFile.value = file.raw;
    previewImage.value = URL.createObjectURL(file.raw);
  };
  
  // 搜索人脸
  const performSearch = async (threshold = 0.6) => {
    if (!selectedFile.value) {
      ElMessage.warning('请先选择图片');
      return null;
    }
    
    loading.value = true;
    error.value = null;
    searched.value = false;
    searchResults.value = [];
    
    try {
      const searchData = {
        file: selectedFile.value,
        threshold
      };
      
      const data = await searchFace(searchData);
      searchResults.value = data.results;
      searched.value = true;
      
      // 更新存储
      searchStore.setCurrentSearch(data);
      searchStore.addSearchHistory(data);
      
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '搜索失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取搜索历史
  const fetchSearchHistories = async (params = {}) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getSearchHistories(params);
      searchStore.setSearchHistories(data);
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取搜索历史失败';
      ElMessage.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  // 获取单个搜索历史
  const fetchSearchHistory = async (searchId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const data = await getSearchHistory(searchId);
      searchStore.setCurrentSearch(data);
      searchResults.value = data.results;
      searched.value = true;
      
      // 设置预览图片
      previewImage.value = getImageUrl(data.image_path, 'unknown');
      
      return data;
    } catch (err) {
      error.value = err.response?.data?.detail || '获取搜索历史失败';
      ElMessage.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  return {
    loading,
    error,
    searchResults,
    previewImage,
    selectedFile,
    searched,
    searchHistories,
    currentSearch,
    handleFileChange,
    performSearch,
    fetchSearchHistories,
    fetchSearchHistory
  };
}
