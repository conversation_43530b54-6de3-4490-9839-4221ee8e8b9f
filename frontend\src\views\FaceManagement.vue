<template>
  <div class="face-management">
    <h1>人脸库管理</h1>

    <div v-if="isLoggedIn">
      <div class="upload-section">
        <h2>添加新人脸</h2>
        <el-form :model="faceForm" label-width="80px">
          <el-form-item label="姓名">
            <el-input v-model="faceForm.name" placeholder="请输入人物姓名"></el-input>
          </el-form-item>
          <el-form-item label="图片">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :show-file-list="false"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                拖拽文件到此处或 <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  请上传包含清晰人脸的JPG/PNG图片
                </div>
              </template>
            </el-upload>
          </el-form-item>

          <div v-if="previewImage" class="preview-container">
            <h3>预览</h3>
            <img :src="previewImage" alt="Preview" class="preview-image" />
          </div>

          <el-form-item>
            <el-button type="primary" @click="addFace" :loading="loading">添加</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="faces-section">
        <h2>已知人脸列表</h2>
        <el-input
          v-model="searchQuery"
          placeholder="搜索人物姓名"
          prefix-icon="Search"
          class="search-input"
        />

        <div class="faces-grid">
          <el-card v-for="face in filteredFaces" :key="face.id" class="face-card">
            <div class="face-content">
              <img :src="getImageUrl(face.image_path)" alt="Face" class="face-image" />
              <div class="face-info">
                <h3>{{ face.name }}</h3>
                <p class="face-date">添加时间: {{ new Date(face.created_at).toLocaleString() }}</p>
              </div>
              <div class="face-actions">
                <el-button type="primary" size="small" @click="editFace(face)">编辑</el-button>
                <el-button type="danger" size="small" @click="confirmDeleteFace(face)">删除</el-button>
              </div>
            </div>
          </el-card>
        </div>

        <div v-if="filteredFaces.length === 0" class="no-faces">
          <el-empty description="暂无人脸数据"></el-empty>
        </div>
      </div>
    </div>

    <div v-else class="login-required">
      <el-empty description="请登录后管理人脸库">
        <el-button type="primary">登录</el-button>
      </el-empty>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑人脸信息" width="30%">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="editForm.name" placeholder="请输入人物姓名"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateFace">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { UploadFilled, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getFaces as getFacesList, createFace as addNewFace, updateFace as updateFaceInfo, deleteFace as deleteFaceById } from '../api/faces';
import { useAuthStore } from '../store/modules/auth';
import { getImageUrl as getImageUrlUtil } from '../utils/format';

// 状态
const isLoggedIn = ref(false);
const faces = ref([]);
const faceForm = ref({ name: '' });
const selectedFile = ref(null);
const previewImage = ref('');
const loading = ref(false);
const searchQuery = ref('');
const editDialogVisible = ref(false);
const editForm = ref({ id: null, name: '' });

// 过滤后的人脸列表
const filteredFaces = computed(() => {
  if (!searchQuery.value) return faces.value;
  const query = searchQuery.value.toLowerCase();
  return faces.value.filter(face => face.name.toLowerCase().includes(query));
});

// 使用 Pinia store 管理认证状态
const authStore = useAuthStore();
isLoggedIn.value = computed(() => authStore.isLoggedIn);

// 获取人脸列表
const getFaces = async () => {
  if (!isLoggedIn.value) return;

  try {
    const data = await getFacesList();
    faces.value = data;
  } catch (error) {
    console.error('Failed to get faces:', error);
    ElMessage.error('获取人脸列表失败');
  }
};

// 处理文件选择
const handleFileChange = (file) => {
  selectedFile.value = file.raw;
  previewImage.value = URL.createObjectURL(file.raw);
};

// 添加人脸
const addFace = async () => {
  if (!faceForm.value.name) {
    ElMessage.warning('请输入人物姓名');
    return;
  }

  if (!selectedFile.value) {
    ElMessage.warning('请选择图片');
    return;
  }

  loading.value = true;

  try {
    await addNewFace({
      name: faceForm.value.name,
      file: selectedFile.value
    });

    ElMessage.success('添加成功');
    faceForm.value.name = '';
    selectedFile.value = null;
    previewImage.value = '';
    await getFaces();
  } catch (error) {
    console.error('Add face failed:', error);
    ElMessage.error('添加失败: ' + (error.response?.data?.detail || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 编辑人脸
const editFace = (face) => {
  editForm.value = {
    id: face.id,
    name: face.name
  };
  editDialogVisible.value = true;
};

// 确认删除人脸
const confirmDeleteFace = (face) => {
  ElMessageBox.confirm(
    `确定要删除 ${face.name} 的人脸信息吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteFaceById(face.id);
      ElMessage.success('删除成功');
      await getFaces();
    } catch (error) {
      console.error('Delete face failed:', error);
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || '未知错误'));
    }
  }).catch(() => {
    // 用户点击取消，不做任何操作
  });
};

// 更新人脸信息
const updateFace = async () => {
  if (!editForm.value.name) {
    ElMessage.warning('请输入人物姓名');
    return;
  }

  try {
    await updateFaceInfo(editForm.value.id, {
      name: editForm.value.name
    });

    ElMessage.success('更新成功');
    editDialogVisible.value = false;
    await getFaces();
  } catch (error) {
    console.error('Update face failed:', error);
    ElMessage.error('更新失败: ' + (error.response?.data?.detail || '未知错误'));
  }

};

// 获取图片URL
const getImageUrl = (path) => {
  return getImageUrlUtil(path, 'known');
};

// 初始化加载
onMounted(async () => {
  if (isLoggedIn.value) {
    await getFaces();
  }
});
</script>

<style scoped>
.face-management {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.upload-section, .faces-section {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.preview-container {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.preview-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.faces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.face-card {
  transition: transform 0.3s;
}

.face-card:hover {
  transform: translateY(-5px);
}

.face-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.face-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.face-info {
  text-align: center;
}

.face-date {
  font-size: 0.9rem;
  color: #909399;
  margin-top: 0.5rem;
}

.face-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.no-faces, .login-required {
  padding: 3rem;
  text-align: center;
}
</style>
