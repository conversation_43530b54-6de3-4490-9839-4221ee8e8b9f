import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'

// 导入视图组件
import Home from './views/Home.vue'
import FaceSearch from './views/FaceSearch.vue'
import FaceManagement from './views/FaceManagement.vue'

// 创建路由
const routes = [
  { path: '/', component: Home },
  { path: '/search', component: FaceSearch },
  { path: '/management', component: FaceManagement }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建Pinia状态管理
const pinia = createPinia()

// 创建应用
const app = createApp(App)

// 使用插件
app.use(router)
app.use(pinia)
app.use(ElementPlus)

// 挂载应用
app.mount('#app')
