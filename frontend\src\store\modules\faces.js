import { defineStore } from 'pinia';

export const useFaceStore = defineStore('faces', {
  state: () => ({
    faces: [],
  }),
  
  getters: {
    totalFaces: (state) => state.faces.length,
  },
  
  actions: {
    setFaces(faces) {
      this.faces = faces;
    },
    
    addFace(face) {
      this.faces.push(face);
    },
    
    updateFace(updatedFace) {
      const index = this.faces.findIndex(face => face.id === updatedFace.id);
      if (index !== -1) {
        this.faces[index] = updatedFace;
      }
    },
    
    removeFace(faceId) {
      this.faces = this.faces.filter(face => face.id !== faceId);
    },
  },
});
