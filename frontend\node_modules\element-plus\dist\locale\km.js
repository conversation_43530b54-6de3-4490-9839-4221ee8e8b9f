/*! Element Plus v2.9.10 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleKm = factory());
})(this, (function () { 'use strict';

  var km = {
    name: "km",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "\u1799\u179B\u17CB\u1796\u17D2\u179A\u1798",
        clear: "\u179B\u17BB\u1794"
      },
      datepicker: {
        now: "\u17A5\u17A1\u17BC\u179C\u200B\u1793\u17C1\u17C7",
        today: "\u1790\u17D2\u1784\u17C3\u1793\u17C1\u17C7",
        cancel: "\u1794\u17C4\u17C7\u1794\u1784\u17CB",
        clear: "\u179B\u17BB\u1794",
        confirm: "\u1799\u179B\u17CB\u1796\u17D2\u179A\u1798",
        selectDate: "\u1787\u17D2\u179A\u17BE\u179F\u179A\u17BE\u179F\u1790\u17D2\u1784\u17C3",
        selectTime: "\u1787\u17D2\u179A\u17BE\u179F\u179A\u17BE\u179F\u1798\u17C9\u17C4\u1784",
        startDate: "\u1790\u17D2\u1784\u17C3\u1785\u17B6\u1794\u17CB\u1795\u17D2\u178F\u17BE\u1798",
        startTime: "\u1798\u17C9\u17C4\u1784\u1785\u17B6\u1794\u17CB\u1795\u17D2\u178F\u17BE\u1798",
        endDate: "\u1790\u17D2\u1784\u17C3\u1794\u1789\u17D2\u1785\u1794\u17CB",
        endTime: "\u1798\u17C9\u17C4\u1784\u1794\u1789\u17D2\u1785\u1794\u17CB",
        prevYear: "\u1786\u17D2\u1793\u17B6\u17C6\u1798\u17BB\u1793",
        nextYear: "\u1786\u17D2\u1793\u17B6\u17C6\u1780\u17D2\u179A\u17C4\u1799",
        prevMonth: "\u1781\u17C2\u1798\u17BB\u1793",
        nextMonth: "\u1781\u17C2\u1780\u17D2\u179A\u17C4\u1799",
        year: "\u1786\u17D2\u1793\u17B6\u17C6",
        month1: "\u1798\u1780\u179A\u17B6",
        month2: "\u1780\u17BB\u1798\u17D2\u1797\u17C8",
        month3: "\u1798\u17B8\u1793\u17B6",
        month4: "\u1798\u17C1\u179F\u17B6",
        month5: "\u17A7\u179F\u1797\u17B6",
        month6: "\u1798\u17B7\u1790\u17BB\u1793\u17B6",
        month7: "\u1780\u1780\u17D2\u1780\u178A\u17B6",
        month8: "\u179F\u17B8\u17A0\u17B6",
        month9: "\u1780\u1789\u17D2\u1789\u17B6",
        month10: "\u178F\u17BB\u179B\u17B6",
        month11: "\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6",
        month12: "\u1792\u17D2\u1793\u17BC",
        weeks: {
          sun: "\u17A2\u17B6\u1791\u17B7\u178F\u17D2\u1799",
          mon: "\u1785\u1793\u17D2\u1791",
          tue: "\u17A2\u1784\u17D2\u1782\u17B6\u179A",
          wed: "\u1796\u17BB\u1792",
          thu: "\u1796\u17D2\u179A\u17A0",
          fri: "\u179F\u17BB\u1780\u17D2\u179A",
          sat: "\u179F\u17C5\u179A\u17CD"
        },
        months: {
          jan: "\u1798\u1780\u179A\u17B6",
          feb: "\u1780\u17BB\u1798\u17D2\u1797\u17C8",
          mar: "\u1798\u17B8\u1793\u17B6",
          apr: "\u1798\u17C1\u179F\u17B6",
          may: "\u17A7\u179F\u1797\u17B6",
          jun: "\u1798\u17B7\u1790\u17BB\u1793\u17B6",
          jul: "\u1780\u1780\u17D2\u1780\u178A\u17B6",
          aug: "\u179F\u17B8\u17A0\u17B6",
          sep: "\u1780\u1789\u17D2\u1789\u17B6",
          oct: "\u178F\u17BB\u179B\u17B6",
          nov: "\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6",
          dec: "\u1792\u17D2\u1793\u17BC"
        }
      },
      select: {
        loading: "\u1780\u17C6\u1796\u17BB\u1784\u1795\u17D2\u1791\u17BB\u1780",
        noMatch: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799\u178A\u17BC\u1785",
        noData: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799",
        placeholder: "\u1787\u17D2\u179A\u17BE\u179F\u179A\u17BE\u179F"
      },
      mention: {
        loading: "\u1780\u17C6\u1796\u17BB\u1784\u1795\u17D2\u1791\u17BB\u1780"
      },
      cascader: {
        noMatch: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799\u178A\u17BC\u1785",
        loading: "\u1780\u17C6\u1796\u17BB\u1784\u1795\u17D2\u1791\u17BB\u1780",
        placeholder: "\u1787\u17D2\u179A\u17BE\u179F\u179A\u17BE\u179F",
        noData: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799"
      },
      pagination: {
        goto: "\u1791\u17C5\u1780\u17B6\u1793\u17CB",
        pagesize: "/\u1791\u17C6\u1796\u17D0\u179A",
        total: "\u179F\u179A\u17BB\u1794 {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u179F\u17B6\u179A",
        confirm: "\u1799\u179B\u17CB\u1796\u17D2\u179A\u1798",
        cancel: "\u1794\u17C4\u17C7\u1794\u1784\u17CB",
        error: "\u1780\u17B6\u179A\u1794\u1789\u17D2\u1785\u17BC\u179B\u1798\u17B7\u1793\u178F\u17D2\u179A\u17BC\u179C\u1794\u17B6\u1793\u17A2\u1793\u17BB\u1789\u17D2\u1789\u17B6\u178F"
      },
      upload: {
        deleteTip: "\u1785\u17BB\u1785\u179B\u17BB\u1794\u178A\u17BE\u1798\u17D2\u1794\u17B8\u178A\u1780\u1785\u17C1\u1789",
        delete: "\u179B\u17BB\u1794",
        preview: "\u1798\u17BE\u179B",
        continue: "\u1794\u1793\u17D2\u178F"
      },
      table: {
        emptyText: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799",
        confirmFilter: "\u1799\u179B\u17CB\u1796\u17D2\u179A\u1798",
        resetFilter: "\u1780\u17C6\u178E\u178F\u17CB\u17A1\u17BE\u1784\u179C\u17B7\u1789",
        clearFilter: "\u1791\u17B6\u17C6\u1784\u17A2\u179F\u17CB",
        sumText: "\u1794\u17BC\u1780"
      },
      tree: {
        emptyText: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799"
      },
      transfer: {
        noMatch: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799\u178A\u17BC\u1785",
        noData: "\u1782\u17D2\u1798\u17B6\u1793\u1791\u17B7\u1793\u17D2\u1793\u1793\u17D0\u1799",
        titles: ["\u1794\u1789\u17D2\u1787\u17B8 \u17E1", "\u1794\u1789\u17D2\u1787\u17B8 \u17E2"],
        filterPlaceholder: "\u1794\u1789\u17D2\u1785\u17BC\u179B\u1796\u17B6\u1780\u17D2\u1799",
        noCheckedFormat: "{total} \u1792\u17B6\u178F\u17BB",
        hasCheckedFormat: "{checked}/{total} \u1794\u17B6\u1793\u1787\u17D2\u179A\u17BE\u179F\u1799\u1780"
      },
      image: {
        error: "\u1798\u17B7\u1793\u1794\u17B6\u1793\u1787\u17C4\u1782\u1787\u17D0\u1799"
      },
      pageHeader: {
        title: "\u178F\u17D2\u179A\u179B\u1794\u17CB\u1780\u17D2\u179A\u17C4\u1799"
      },
      popconfirm: {
        confirmButtonText: "\u1799\u179B\u17CB\u1796\u17D2\u179A\u1798",
        cancelButtonText: "\u1798\u17B7\u1793\u1796\u17D2\u179A\u1798"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return km;

}));
