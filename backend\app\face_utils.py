import face_recognition
import numpy as np
import os
import pickle
from PIL import Image
from typing import List, Tuple, Optional
import shutil
from datetime import datetime

# 上传目录
UPLOAD_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "uploads")
KNOWN_FACES_DIR = os.path.join(UPLOAD_DIR, "known")
UNKNOWN_FACES_DIR = os.path.join(UPLOAD_DIR, "unknown")

# 确保目录存在
os.makedirs(KNOWN_FACES_DIR, exist_ok=True)
os.makedirs(UNKNOWN_FACES_DIR, exist_ok=True)

def save_uploaded_image(file, directory: str, filename: Optional[str] = None) -> str:
    """保存上传的图片到指定目录"""
    if filename is None:
        # 使用时间戳作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
    
    file_path = os.path.join(directory, filename)
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    return file_path

def detect_faces(image_path: str) -> List[np.ndarray]:
    """检测图片中的人脸并返回人脸编码列表"""
    # 加载图片
    image = face_recognition.load_image_file(image_path)
    
    # 检测人脸位置
    face_locations = face_recognition.face_locations(image)
    
    # 如果没有检测到人脸，返回空列表
    if not face_locations:
        return []
    
    # 提取人脸特征
    face_encodings = face_recognition.face_encodings(image, face_locations)
    
    return face_encodings

def serialize_encoding(encoding: np.ndarray) -> bytes:
    """将人脸编码序列化为二进制数据"""
    return pickle.dumps(encoding)

def deserialize_encoding(data: bytes) -> np.ndarray:
    """将二进制数据反序列化为人脸编码"""
    return pickle.loads(data)

def compare_faces(known_encoding: np.ndarray, unknown_encoding: np.ndarray) -> Tuple[bool, float]:
    """比较两个人脸编码，返回是否匹配和相似度"""
    # 计算人脸距离
    face_distance = face_recognition.face_distance([known_encoding], unknown_encoding)[0]
    
    # 转换为相似度（0-1之间，越大越相似）
    similarity = 1 - face_distance
    
    # 判断是否匹配（阈值可调整）
    is_match = similarity > 0.6
    
    return is_match, similarity

def crop_face(image_path: str, face_location: Tuple[int, int, int, int], output_path: str) -> None:
    """裁剪图片中的人脸并保存"""
    # 加载图片
    image = Image.open(image_path)
    
    # 人脸位置（top, right, bottom, left）
    top, right, bottom, left = face_location
    
    # 裁剪人脸
    face_image = image.crop((left, top, right, bottom))
    
    # 保存裁剪后的人脸
    face_image.save(output_path)
