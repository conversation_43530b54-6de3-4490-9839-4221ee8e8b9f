from sqlalchemy import Column, Integer, String, Float, LargeBinary, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_admin = Column(Integer, default=0)  # 0: 普通用户, 1: 管理员
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    faces = relationship("Face", back_populates="owner")

class Face(Base):
    __tablename__ = "faces"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)  # 人物姓名
    image_path = Column(String)  # 图片存储路径
    face_encoding = Column(LargeBinary)  # 人脸特征向量，使用pickle序列化的numpy数组
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    owner = relationship("User", back_populates="faces")

class SearchHistory(Base):
    __tablename__ = "search_history"

    id = Column(Integer, primary_key=True, index=True)
    image_path = Column(String)  # 搜索图片存储路径
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 可以为匿名用户
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    results = relationship("SearchResult", back_populates="search")

class SearchResult(Base):
    __tablename__ = "search_results"

    id = Column(Integer, primary_key=True, index=True)
    search_id = Column(Integer, ForeignKey("search_history.id"))
    face_id = Column(Integer, ForeignKey("faces.id"))
    similarity = Column(Float)  # 相似度得分
    
    # 关系
    search = relationship("SearchHistory", back_populates="results")
    face = relationship("Face")
