import { defineStore } from 'pinia';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: null,
    redirectPath: null,
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.user?.is_admin === 1,
  },
  
  actions: {
    setToken(token) {
      this.token = token;
      localStorage.setItem('token', token);
    },
    
    setUser(user) {
      this.user = user;
    },
    
    logout() {
      this.token = null;
      this.user = null;
      this.redirectPath = null;
      localStorage.removeItem('token');
    },

    setRedirectPath(path) {
      this.redirectPath = path;
    },

    getRedirectPath() {
      const path = this.redirectPath;
      this.redirectPath = null;
      return path || '/';
    },
  },
});
