<template>
  <div class="user-profile">
    <h1>个人资料</h1>
    
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>基本信息</h2>
        </div>
      </template>
      
      <div class="profile-info">
        <div class="info-item">
          <span class="label">用户名:</span>
          <span class="value">{{ user.username }}</span>
        </div>
        <div class="info-item">
          <span class="label">邮箱:</span>
          <span class="value">{{ user.email }}</span>
        </div>
        <div class="info-item">
          <span class="label">角色:</span>
          <span class="value">{{ user.is_admin === 1 ? '管理员' : '普通用户' }}</span>
        </div>
        <div class="info-item">
          <span class="label">注册时间:</span>
          <span class="value">{{ formatDateTime(user.created_at) }}</span>
        </div>
      </div>
    </el-card>
    
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>统计信息</h2>
        </div>
      </template>
      
      <div class="profile-stats">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="stat-item">
              <el-statistic title="我的人脸数量" :value="facesCount">
                <template #suffix>
                  <el-icon><User /></el-icon>
                </template>
              </el-statistic>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <el-statistic title="搜索历史数量" :value="searchCount">
                <template #suffix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-statistic>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { User, Search } from '@element-plus/icons-vue';
import { useAuth } from '../../composables/useAuth';
import { useFaces } from '../../composables/useFaces';
import { formatDateTime } from '../../utils/format';
import { getFaces } from '../../api/faces';
import { getSearchHistories } from '../../api/search';

// 获取认证信息
const { user } = useAuth();

// 统计数据
const facesCount = ref(0);
const searchCount = ref(0);

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取用户的人脸数量
    const faces = await getFaces();
    facesCount.value = faces.length;
    
    // 获取用户的搜索历史数量
    const histories = await getSearchHistories();
    searchCount.value = histories.length;
  } catch (error) {
    console.error('Failed to fetch stats:', error);
  }
};

// 组件挂载时获取统计数据
onMounted(() => {
  fetchStats();
});
</script>

<style scoped>
.user-profile {
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 2rem;
}

.profile-card {
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-info {
  padding: 1rem 0;
}

.info-item {
  margin-bottom: 1rem;
  display: flex;
}

.label {
  font-weight: bold;
  width: 100px;
}

.value {
  flex: 1;
}

.profile-stats {
  padding: 1rem 0;
}

.stat-item {
  text-align: center;
  padding: 1rem;
}
</style>
