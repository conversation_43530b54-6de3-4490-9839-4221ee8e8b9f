from fastapi import APIRouter

from app.api.endpoints import auth, faces, search, categories, aliases

# 创建API路由器
api_router = APIRouter()

# 包含各个端点路由器
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(faces.router, prefix="/faces", tags=["faces"])
api_router.include_router(search.router, prefix="/search", tags=["search"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(aliases.router, prefix="/aliases", tags=["aliases"])
