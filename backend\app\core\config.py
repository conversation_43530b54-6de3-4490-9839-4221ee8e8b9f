import os
import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, BaseSettings, EmailStr, HttpUrl, PostgresDsn, validator


class Settings(BaseSettings):
    """应用配置类，使用pydantic进行环境变量验证"""
    
    # 基础配置
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 项目信息
    PROJECT_NAME: str = "Face Recognition API"
    DESCRIPTION: str = "Face Recognition Search System API"
    VERSION: str = "0.1.0"
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI: str = "sqlite:///./face_recognition.db"
    
    # 文件上传配置
    UPLOAD_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "uploads")
    KNOWN_FACES_DIR: str = os.path.join(UPLOAD_DIR, "known")
    UNKNOWN_FACES_DIR: str = os.path.join(UPLOAD_DIR, "unknown")
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png"]
    
    # 人脸识别配置
    FACE_SIMILARITY_THRESHOLD: float = 0.6
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
    
    class Config:
        case_sensitive = True
        env_file = ".env"


# 创建全局设置对象
settings = Settings()

# 确保目录存在
os.makedirs(settings.KNOWN_FACES_DIR, exist_ok=True)
os.makedirs(settings.UNKNOWN_FACES_DIR, exist_ok=True)
os.makedirs(settings.LOG_DIR, exist_ok=True)
