import apiClient from './index';

/**
 * 搜索人脸
 * @param {Object} searchData - 搜索数据
 * @param {File} searchData.file - 搜索图片文件
 * @param {number} searchData.threshold - 相似度阈值（可选）
 * @returns {Promise} - 返回搜索结果
 */
export const searchFace = async (searchData) => {
  const formData = new FormData();
  formData.append('file', searchData.file);
  
  if (searchData.threshold) {
    formData.append('threshold', searchData.threshold);
  }
  
  const response = await apiClient.post('/search/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

/**
 * 获取搜索历史列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的最大记录数
 * @returns {Promise} - 返回搜索历史列表
 */
export const getSearchHistories = async (params = {}) => {
  const response = await apiClient.get('/search/', { params });
  return response.data;
};

/**
 * 获取单个搜索历史
 * @param {number} searchId - 搜索历史ID
 * @returns {Promise} - 返回搜索历史信息
 */
export const getSearchHistory = async (searchId) => {
  const response = await apiClient.get(`/search/${searchId}`);
  return response.data;
};
