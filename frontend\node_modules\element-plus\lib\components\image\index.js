'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var image$1 = require('./src/image.js');
var image = require('./src/image2.js');
var install = require('../../utils/vue/install.js');

const ElImage = install.withInstall(image$1["default"]);

exports.imageEmits = image.imageEmits;
exports.imageProps = image.imageProps;
exports.ElImage = ElImage;
exports["default"] = ElImage;
//# sourceMappingURL=index.js.map
