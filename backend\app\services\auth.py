from typing import Optional
from datetime import timed<PERSON>ta

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt, JWTError
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import verify_password, create_access_token
from app.core.exceptions import UnauthorizedException, ForbiddenException
from app.db.session import get_db
from app.models.user import User
from app.schemas.token import TokenPayload
from app.services.user import get_user_by_username

# OAuth2 密码流
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    验证用户

    Args:
        db: 数据库会话
        username: 用户名
        password: 密码

    Returns:
        Optional[User]: 验证成功返回用户对象，否则返回None
    """
    user = get_user_by_username(db, username=username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    """
    获取当前用户

    Args:
        db: 数据库会话
        token: JWT令牌

    Returns:
        User: 当前用户

    Raises:
        UnauthorizedException: 如果令牌无效或用户不存在
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=["HS256"]
        )
        token_data = TokenPayload(**payload)
    except (JWTError, ValidationError):
        raise UnauthorizedException()

    user = get_user_by_username(db, username=token_data.sub)
    if not user:
        raise UnauthorizedException()

    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃用户

    Args:
        current_user: 当前用户

    Returns:
        User: 当前活跃用户
    """
    return current_user


def get_current_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前管理员用户

    Args:
        current_user: 当前用户

    Returns:
        User: 当前管理员用户

    Raises:
        ForbiddenException: 如果用户不是管理员
    """
    if current_user.is_admin != 1:
        raise ForbiddenException()

    return current_user


def create_user_access_token(user: User) -> str:
    """
    创建用户访问令牌

    Args:
        user: 用户对象

    Returns:
        str: JWT访问令牌
    """
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )
