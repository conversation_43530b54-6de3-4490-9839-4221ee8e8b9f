import logging
from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.category import Category
from app.models.face import Face
from app.schemas.category import CategoryCreate, CategoryUpdate

# 设置日志记录器
logger = logging.getLogger(__name__)


def get_category(db: Session, category_id: int) -> Optional[Category]:
    """
    通过ID获取分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        
    Returns:
        Optional[Category]: 分类对象，如果不存在则返回None
    """
    return db.query(Category).filter(Category.id == category_id).first()


def get_categories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    user_id: Optional[int] = None
) -> List[Category]:
    """
    获取分类列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的最大记录数
        user_id: 用户ID（可选，用于筛选特定用户的分类）
        
    Returns:
        List[Category]: 分类列表
    """
    query = db.query(Category)
    if user_id is not None:
        query = query.filter(Category.user_id == user_id)
    return query.offset(skip).limit(limit).all()


def create_category(
    db: Session, 
    category_data: CategoryCreate, 
    user_id: int
) -> Category:
    """
    创建新分类
    
    Args:
        db: 数据库会话
        category_data: 分类创建模式
        user_id: 用户ID
        
    Returns:
        Category: 创建的分类对象
    """
    db_category = Category(
        name=category_data.name,
        description=category_data.description,
        user_id=user_id
    )
    
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    logger.info(f"Created category: {db_category.id} - {db_category.name}")
    return db_category


def update_category(
    db: Session, 
    category_id: int, 
    category_data: CategoryUpdate
) -> Optional[Category]:
    """
    更新分类信息
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        category_data: 分类更新模式
        
    Returns:
        Optional[Category]: 更新后的分类对象，如果分类不存在则返回None
    """
    db_category = get_category(db, category_id=category_id)
    if not db_category:
        return None
    
    update_data = category_data.dict(exclude_unset=True)
    
    # 更新分类属性
    for field, value in update_data.items():
        if hasattr(db_category, field):
            setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    
    logger.info(f"Updated category: {db_category.id} - {db_category.name}")
    return db_category


def delete_category(db: Session, category_id: int) -> bool:
    """
    删除分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        
    Returns:
        bool: 是否成功删除
    """
    db_category = get_category(db, category_id=category_id)
    if not db_category:
        return False
    
    # 删除数据库记录
    db.delete(db_category)
    db.commit()
    
    logger.info(f"Deleted category: {category_id}")
    return True


def check_category_owner(db: Session, category_id: int, user_id: int) -> bool:
    """
    检查分类是否属于指定用户
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        user_id: 用户ID
        
    Returns:
        bool: 分类是否属于指定用户
    """
    db_category = get_category(db, category_id=category_id)
    if not db_category:
        return False
    
    return db_category.user_id == user_id


def add_faces_to_category(db: Session, category_id: int, face_ids: List[int]) -> int:
    """
    将多个人脸添加到分类中
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        face_ids: 人脸ID列表
        
    Returns:
        int: 成功添加的人脸数量
    """
    db_category = get_category(db, category_id=category_id)
    if not db_category:
        return 0
    
    # 获取所有有效的人脸
    faces = db.query(Face).filter(Face.id.in_(face_ids)).all()
    
    # 添加到分类中
    for face in faces:
        if face not in db_category.faces:
            db_category.faces.append(face)
    
    db.commit()
    
    logger.info(f"Added {len(faces)} faces to category: {category_id}")
    return len(faces)


def remove_faces_from_category(db: Session, category_id: int, face_ids: List[int]) -> int:
    """
    从分类中移除多个人脸
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        face_ids: 人脸ID列表
        
    Returns:
        int: 成功移除的人脸数量
    """
    db_category = get_category(db, category_id=category_id)
    if not db_category:
        return 0
    
    # 获取所有有效的人脸
    faces = db.query(Face).filter(Face.id.in_(face_ids)).all()
    
    # 从分类中移除
    removed_count = 0
    for face in faces:
        if face in db_category.faces:
            db_category.faces.remove(face)
            removed_count += 1
    
    db.commit()
    
    logger.info(f"Removed {removed_count} faces from category: {category_id}")
    return removed_count


def update_face_categories(db: Session, face_id: int, category_ids: List[int]) -> int:
    """
    更新人脸的分类
    
    Args:
        db: 数据库会话
        face_id: 人脸ID
        category_ids: 分类ID列表
        
    Returns:
        int: 成功设置的分类数量
    """
    db_face = db.query(Face).filter(Face.id == face_id).first()
    if not db_face:
        return 0
    
    # 获取所有有效的分类
    categories = db.query(Category).filter(Category.id.in_(category_ids)).all()
    
    # 清除现有分类
    db_face.categories = []
    
    # 设置新分类
    for category in categories:
        db_face.categories.append(category)
    
    db.commit()
    
    logger.info(f"Updated categories for face: {face_id}, set {len(categories)} categories")
    return len(categories)


def bulk_update_face_categories(db: Session, face_ids: List[int], category_ids: List[int]) -> int:
    """
    批量更新多个人脸的分类
    
    Args:
        db: 数据库会话
        face_ids: 人脸ID列表
        category_ids: 分类ID列表
        
    Returns:
        int: 成功更新的人脸数量
    """
    # 获取所有有效的人脸
    faces = db.query(Face).filter(Face.id.in_(face_ids)).all()
    
    # 获取所有有效的分类
    categories = db.query(Category).filter(Category.id.in_(category_ids)).all()
    
    # 更新每个人脸的分类
    for face in faces:
        # 清除现有分类
        face.categories = []
        
        # 设置新分类
        for category in categories:
            face.categories.append(category)
    
    db.commit()
    
    logger.info(f"Bulk updated categories for {len(faces)} faces, set {len(categories)} categories")
    return len(faces)
