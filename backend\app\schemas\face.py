from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel

from app.schemas.category import Category


# 共享属性
class FaceBase(BaseModel):
    """
    人脸基础模式
    """
    name: Optional[str] = None
    description: Optional[str] = None


class FaceCreate(FaceBase):
    """
    人脸创建模式
    """
    name: str


class FaceUpdate(FaceBase):
    """
    人脸更新模式
    """
    pass


class FaceInDBBase(FaceBase):
    """
    数据库中的人脸基础模式
    """
    id: int
    name: str
    image_path: str
    user_id: int
    created_at: datetime

    class Config:
        orm_mode = True


class Face(FaceInDBBase):
    """
    人脸模式（返回给API的）
    """
    categories: List[Category] = []


class FaceInDB(FaceInDBBase):
    """
    数据库中的人脸模式（包含编码）
    """
    face_encoding: bytes


class FaceBulkUploadResult(BaseModel):
    """
    批量上传结果
    """
    total: int
    success: int
    failed: int
    faces: List[Face] = []
    failed_files: List[str] = []
