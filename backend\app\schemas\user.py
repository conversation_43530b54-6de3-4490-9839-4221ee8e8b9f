from typing import Optional
from datetime import datetime

from pydantic import BaseModel, EmailStr


# 共享属性
class UserBase(BaseModel):
    """
    用户基础模式
    """
    username: Optional[str] = None
    email: Optional[EmailStr] = None


class UserCreate(UserBase):
    """
    用户创建模式
    """
    username: str
    email: EmailStr
    password: str


class UserUpdate(UserBase):
    """
    用户更新模式
    """
    password: Optional[str] = None


class UserInDBBase(UserBase):
    """
    数据库中的用户基础模式
    """
    id: int
    username: str
    email: EmailStr
    is_admin: int
    created_at: datetime

    class Config:
        from_attributes = True


class User(UserInDBBase):
    """
    用户模式（返回给API的）
    """
    pass


class UserInDB(UserInDBBase):
    """
    数据库中的用户模式（包含哈希密码）
    """
    hashed_password: str
