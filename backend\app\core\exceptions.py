from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class BaseAPIException(HTTPException):
    """
    基础API异常类
    """
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail: str = "Server Error"
    
    def __init__(
        self, 
        detail: Optional[str] = None, 
        headers: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(
            status_code=self.status_code,
            detail=detail or self.detail,
            headers=headers,
        )


class NotFoundException(BaseAPIException):
    """
    资源未找到异常
    """
    status_code = status.HTTP_404_NOT_FOUND
    detail = "Resource not found"


class UnauthorizedException(BaseAPIException):
    """
    未授权异常
    """
    status_code = status.HTTP_401_UNAUTHORIZED
    detail = "Not authenticated"
    
    def __init__(
        self, 
        detail: Optional[str] = None, 
        headers: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(
            detail=detail or self.detail,
            headers={"WWW-Authenticate": "Bearer", **(headers or {})}
        )


class ForbiddenException(BaseAPIException):
    """
    禁止访问异常
    """
    status_code = status.HTTP_403_FORBIDDEN
    detail = "Not enough permissions"


class BadRequestException(BaseAPIException):
    """
    错误请求异常
    """
    status_code = status.HTTP_400_BAD_REQUEST
    detail = "Bad request"


class ValidationException(BadRequestException):
    """
    验证错误异常
    """
    detail = "Validation error"


class FaceDetectionException(BadRequestException):
    """
    人脸检测异常
    """
    detail = "Face detection error"


class NoFaceDetectedException(FaceDetectionException):
    """
    未检测到人脸异常
    """
    detail = "No face detected in the image"


class FileUploadException(BadRequestException):
    """
    文件上传异常
    """
    detail = "File upload error"


class InvalidFileTypeException(FileUploadException):
    """
    无效文件类型异常
    """
    detail = "Invalid file type"


class FileTooLargeException(FileUploadException):
    """
    文件过大异常
    """
    detail = "File too large"
