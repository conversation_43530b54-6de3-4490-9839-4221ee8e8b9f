from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base

# 人脸与分类的多对多关系表
face_category = Table(
    "face_category",
    Base.metadata,
    Column("face_id", Integer, ForeignKey("face.id"), primary_key=True),
    Column("category_id", Integer, ForeignKey("category.id"), primary_key=True)
)

class Category(Base):
    """
    人脸分类数据模型
    """
    # 基本信息
    name = Column(String, index=True, nullable=False)  # 分类名称
    description = Column(String, nullable=True)  # 分类描述
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    owner = relationship("User", back_populates="categories")
    faces = relationship("Face", secondary=face_category, back_populates="categories")
