<template>
  <div class="home">
    <section class="hero">
      <h1>人脸识别搜索系统</h1>
      <p>基于先进的人脸识别技术，快速准确地搜索和管理人脸数据</p>
      <div class="hero-actions">
        <el-button type="primary" size="large" @click="$router.push('/search')">开始搜索</el-button>
        <el-button size="large" @click="$router.push('/management')">管理人脸库</el-button>
      </div>
    </section>

    <section class="features">
      <h2>主要功能</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <el-icon size="40"><Search /></el-icon>
          <h3>人脸搜索</h3>
          <p>上传图片，快速搜索匹配的人脸，获取详细信息</p>
        </div>
        <div class="feature-card">
          <el-icon size="40"><Upload /></el-icon>
          <h3>人脸库管理</h3>
          <p>轻松上传、编辑和管理已知人脸信息</p>
        </div>
        <div class="feature-card">
          <el-icon size="40"><DataAnalysis /></el-icon>
          <h3>高精度匹配</h3>
          <p>采用先进算法，提供高精度的人脸匹配结果</p>
        </div>
      </div>
    </section>

    <section class="how-it-works">
      <h2>工作原理</h2>
      <div class="steps">
        <div class="step">
          <div class="step-number">1</div>
          <h3>上传图片</h3>
          <p>上传包含人脸的图片到系统</p>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <h3>人脸检测</h3>
          <p>系统自动检测图片中的人脸</p>
        </div>
        <div class="step">
          <div class="step-number">3</div>
          <h3>特征提取</h3>
          <p>提取人脸的独特特征向量</p>
        </div>
        <div class="step">
          <div class="step-number">4</div>
          <h3>匹配搜索</h3>
          <p>与数据库中的人脸进行比对</p>
        </div>
        <div class="step">
          <div class="step-number">5</div>
          <h3>结果展示</h3>
          <p>显示匹配结果和相似度</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { Search, Upload, DataAnalysis } from '@element-plus/icons-vue';
</script>

<style scoped>
.home {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.hero {
  text-align: center;
  padding: 4rem 1rem;
  background-color: #f0f9ff;
  border-radius: 8px;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #303133;
}

.hero p {
  font-size: 1.2rem;
  color: #606266;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.features, .how-it-works {
  padding: 2rem 1rem;
}

.features h2, .how-it-works h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  color: #303133;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  margin: 1rem 0;
  color: #303133;
}

.feature-card p {
  color: #606266;
}

.steps {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.step {
  flex: 1;
  min-width: 200px;
  text-align: center;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.step-number {
  width: 40px;
  height: 40px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-weight: bold;
}

.step h3 {
  margin-bottom: 0.5rem;
  color: #303133;
}

.step p {
  color: #606266;
}
</style>
