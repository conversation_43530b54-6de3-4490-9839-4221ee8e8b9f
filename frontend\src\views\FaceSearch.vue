<template>
  <div class="face-search">
    <h1>人脸搜索</h1>

    <div class="search-container">
      <div class="upload-section">
        <h2>上传图片进行搜索</h2>
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请上传包含清晰人脸的JPG/PNG图片
            </div>
          </template>
        </el-upload>

        <div v-if="previewImage" class="preview-container">
          <h3>预览</h3>
          <img :src="previewImage" alt="Preview" class="preview-image" />
          <el-button type="primary" @click="searchFace" :loading="loading">开始搜索</el-button>
        </div>
      </div>

      <div v-if="searchResults.length > 0" class="results-section">
        <h2>搜索结果</h2>
        <p>找到 {{ searchResults.length }} 个匹配结果</p>

        <div class="results-grid">
          <el-card v-for="result in searchResults" :key="result.id" class="result-card">
            <div class="result-content">
              <img :src="getImageUrl(result.face.image_path)" alt="Face" class="result-image" />
              <div class="result-info">
                <h3>{{ result.face.name }}</h3>
                <p class="similarity">
                  相似度: <span :class="getSimilarityClass(result.similarity)">{{ (result.similarity * 100).toFixed(2) }}%</span>
                </p>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <div v-else-if="searched && !loading" class="no-results">
        <el-empty description="未找到匹配的人脸"></el-empty>
      </div>
    </div>

    <div v-if="isLoggedIn" class="search-history">
      <h2>搜索历史</h2>
      <el-table :data="searchHistory" style="width: 100%">
        <el-table-column prop="created_at" label="搜索时间" width="180">
          <template #default="scope">
            {{ new Date(scope.row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="搜索图片" width="120">
          <template #default="scope">
            <img :src="getImageUrl(scope.row.image_path)" alt="Search Image" class="history-image" />
          </template>
        </el-table-column>
        <el-table-column prop="results.length" label="匹配结果数" width="120" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="viewSearchResult(scope.row)">查看结果</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { getImageUrl as getImageUrlUtil } from '../utils/format';

// 状态
const selectedFile = ref(null);
const previewImage = ref('');
const loading = ref(false);
const searched = ref(false);
const searchResults = ref([]);
const searchHistory = ref([]);
const isLoggedIn = ref(false);

// 检查是否已登录
const checkAuth = () => {
  const token = localStorage.getItem('token');
  isLoggedIn.value = !!token;
};

// 获取搜索历史
const getSearchHistory = async () => {
  if (!isLoggedIn.value) return;

  const token = localStorage.getItem('token');
  try {
    const response = await axios.get('/api/v1/search/', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    searchHistory.value = response.data;
  } catch (error) {
    console.error('Failed to get search history:', error);
  }
};

// 处理文件选择
const handleFileChange = (file) => {
  selectedFile.value = file.raw;
  previewImage.value = URL.createObjectURL(file.raw);
};

// 搜索人脸
const searchFace = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片');
    return;
  }

  loading.value = true;
  searched.value = false;
  searchResults.value = [];

  try {
    const formData = new FormData();
    formData.append('file', selectedFile.value);

    const headers = {};
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await axios.post('/api/v1/search/', formData, { headers });
    searchResults.value = response.data.results;
    searched.value = true;

    // 刷新搜索历史
    if (isLoggedIn.value) {
      getSearchHistory();
    }
  } catch (error) {
    console.error('Search failed:', error);
    ElMessage.error('搜索失败: ' + (error.response?.data?.detail || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 查看搜索结果
const viewSearchResult = async (search) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`/api/v1/search/${search.id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    searchResults.value = response.data.results;
    searched.value = true;
    previewImage.value = getImageUrl(response.data.image_path, 'unknown');
  } catch (error) {
    console.error('Failed to get search result:', error);
    ElMessage.error('获取搜索结果失败');
  }
};

// 获取图片URL
const getImageUrl = (path, forceType = null) => {
  // 如果强制指定类型，使用指定的类型
  if (forceType) {
    return getImageUrlUtil(path, forceType);
  }
  // 否则根据路径判断类型
  const type = path && path.includes('known') ? 'known' : 'unknown';
  return getImageUrlUtil(path, type);
};

// 获取相似度样式类
const getSimilarityClass = (similarity) => {
  if (similarity >= 0.8) return 'similarity-high';
  if (similarity >= 0.6) return 'similarity-medium';
  return 'similarity-low';
};

// 组件挂载时
onMounted(() => {
  checkAuth();
  getSearchHistory();
});
</script>

<style scoped>
.face-search {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.upload-section {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.preview-container {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.preview-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-section {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.result-card {
  transition: transform 0.3s;
}

.result-card:hover {
  transform: translateY(-5px);
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.result-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.result-info {
  text-align: center;
}

.similarity {
  margin-top: 0.5rem;
}

.similarity-high {
  color: #67c23a;
  font-weight: bold;
}

.similarity-medium {
  color: #e6a23c;
  font-weight: bold;
}

.similarity-low {
  color: #f56c6c;
  font-weight: bold;
}

.no-results {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.search-history {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.history-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}
</style>
