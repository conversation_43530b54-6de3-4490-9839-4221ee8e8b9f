from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api.deps import get_db_session, get_current_active_user_dependency
from app.core.exceptions import BadRequestException
from app.models.user import User
from app.schemas.token import Token
from app.schemas.user import User as UserSchema, UserCreate
from app.services.auth import authenticate_user, create_user_access_token
from app.services.user import create_user, get_user_by_email, get_user_by_username

router = APIRouter()


@router.post("/login", response_model=Token)
def login_access_token(
    db: Session = Depends(get_db_session),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 兼容的令牌登录，获取访问令牌
    """
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = create_user_access_token(user)
    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/register", response_model=UserSchema)
def register_user(
    user_in: UserCreate,
    db: Session = Depends(get_db_session)
) -> Any:
    """
    注册新用户
    """
    # 检查用户名是否已存在
    user = get_user_by_username(db, username=user_in.username)
    if user:
        raise BadRequestException("Username already registered")
    
    # 检查邮箱是否已存在
    user = get_user_by_email(db, email=user_in.email)
    if user:
        raise BadRequestException("Email already registered")
    
    # 创建新用户
    user = create_user(db=db, user_in=user_in)
    return user


@router.get("/me", response_model=UserSchema)
def read_users_me(
    current_user: User = Depends(get_current_active_user_dependency)
) -> Any:
    """
    获取当前用户信息
    """
    return current_user
