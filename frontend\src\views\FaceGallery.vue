<template>
  <div class="face-gallery">
    <h1>人脸展示</h1>
    
    <div class="search-filters">
      <div class="filter-row">
        <el-input
          v-model="searchQuery"
          placeholder="搜索人物姓名..."
          class="search-input"
          :prefix-icon="Search"
          clearable
        />
        
        <el-select
          v-model="selectedCategories"
          multiple
          placeholder="选择分类筛选"
          class="category-select"
          clearable
        >
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
        
        <el-button type="primary" @click="searchFaces">搜索</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </div>

    <div class="gallery-stats">
      <p>共找到 {{ filteredFaces.length }} 个人脸</p>
    </div>

    <div v-if="loading" class="loading">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="filteredFaces.length === 0" class="no-results">
      <el-empty description="未找到匹配的人脸">
        <el-button type="primary" @click="resetFilters">重置筛选条件</el-button>
      </el-empty>
    </div>

    <div v-else class="gallery-grid">
      <el-card v-for="face in paginatedFaces" :key="face.id" class="face-card" shadow="hover">
        <div class="face-content">
          <div class="face-image-container">
            <img :src="getImageUrl(face.image_path)" alt="Face" class="face-image" />
          </div>
          
          <div class="face-info">
            <h3 class="face-name">{{ face.name }}</h3>
            
            <div v-if="face.categories && face.categories.length > 0" class="face-categories">
              <el-tag
                v-for="category in face.categories"
                :key="category.id"
                size="small"
                class="category-tag"
              >
                {{ category.name }}
              </el-tag>
            </div>
            
            <p class="face-date">
              添加时间: {{ new Date(face.created_at).toLocaleString() }}
            </p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div v-if="filteredFaces.length > pageSize" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="filteredFaces.length"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getFaces } from '../api/faces';
import { getCategories } from '../api/categories';
import { getImageUrl as getImageUrlUtil } from '../utils/format';

// 状态
const faces = ref([]);
const categories = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedCategories = ref([]);
const currentPage = ref(1);
const pageSize = ref(12);

// 计算属性
const filteredFaces = computed(() => {
  let result = faces.value;

  // 按姓名筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(face => 
      face.name.toLowerCase().includes(query)
    );
  }

  // 按分类筛选
  if (selectedCategories.value.length > 0) {
    result = result.filter(face => {
      if (!face.categories || face.categories.length === 0) {
        return false;
      }
      return face.categories.some(category => 
        selectedCategories.value.includes(category.id)
      );
    });
  }

  return result;
});

const paginatedFaces = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredFaces.value.slice(start, end);
});

// 方法
const getFacesList = async () => {
  loading.value = true;
  try {
    const data = await getFaces();
    faces.value = data;
  } catch (error) {
    console.error('Failed to get faces:', error);
    ElMessage.error('获取人脸列表失败');
  } finally {
    loading.value = false;
  }
};

const getCategoriesList = async () => {
  try {
    const data = await getCategories();
    categories.value = data;
  } catch (error) {
    console.error('Failed to get categories:', error);
    ElMessage.error('获取分类列表失败');
  }
};

const searchFaces = () => {
  currentPage.value = 1; // 重置到第一页
};

const resetFilters = () => {
  searchQuery.value = '';
  selectedCategories.value = [];
  currentPage.value = 1;
};

const handlePageChange = (page) => {
  currentPage.value = page;
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

const getImageUrl = (path) => {
  return getImageUrlUtil(path, 'known');
};

// 初始化
onMounted(async () => {
  await Promise.all([
    getFacesList(),
    getCategoriesList()
  ]);
});
</script>

<style scoped>
.face-gallery {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  text-align: center;
}

.search-filters {
  background-color: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
}

.category-select {
  min-width: 200px;
}

.gallery-stats {
  text-align: center;
  color: #666;
}

.loading {
  padding: 2rem;
}

.no-results {
  padding: 3rem;
  text-align: center;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.face-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.face-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.face-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.face-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.face-image {
  width: 180px;
  height: 180px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.face-info {
  text-align: center;
  width: 100%;
}

.face-name {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.face-categories {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.category-tag {
  margin: 0;
}

.face-date {
  font-size: 0.9rem;
  color: #909399;
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input,
  .category-select {
    min-width: auto;
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
</style>
