import os
import io
import pickle
import shutil
import logging
from datetime import datetime
from typing import List, Tuple, Optional, Dict, Any

import numpy as np
import cv2
import face_recognition
from PIL import Image, ImageOps
from fastapi import UploadFile

from app.core.config import settings
from app.core.exceptions import (
    NoFaceDetectedException,
    InvalidFileTypeException,
    FileTooLargeException
)

# 设置日志记录器
logger = logging.getLogger(__name__)


def validate_image(file: UploadFile) -> None:
    """
    验证上传的图片

    检查文件类型、大小和格式，确保图片可以被face_recognition处理

    Args:
        file: 上传的文件

    Raises:
        InvalidFileTypeException: 如果文件类型不支持或图片格式无效
        FileTooLargeException: 如果文件过大
    """
    # 检查文件类型
    ext = file.filename.split(".")[-1].lower()
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise InvalidFileTypeException(
            f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )

    # 检查文件大小
    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)

    if file_size > settings.MAX_UPLOAD_SIZE:
        max_size_mb = settings.MAX_UPLOAD_SIZE / (1024 * 1024)
        raise FileTooLargeException(
            f"File too large. Maximum size: {max_size_mb:.1f}MB"
        )

    # 尝试用 PIL 打开图片以验证格式
    try:
        image_data = file.file.read()
        img = Image.open(io.BytesIO(image_data))
        if img.format not in ['JPEG', 'PNG']:
            raise InvalidFileTypeException("Image must be in JPEG or PNG format")
        file.file.seek(0)  # 重置文件指针
    except Exception as e:
        file.file.seek(0)  # 重置文件指针
        raise InvalidFileTypeException(f"Invalid image format: {str(e)}")


def save_uploaded_image(file: UploadFile, directory: str, filename: Optional[str] = None) -> str:
    """
    保存上传的图片到指定目录，确保图片格式正确

    Args:
        file: 上传的文件
        directory: 保存目录
        filename: 文件名（可选）

    Returns:
        str: 保存的文件路径
    """
    # 验证图片格式和大小
    logger.info(f"Starting to process uploaded file: {file.filename}")
    validate_image(file)

    # 准备文件路径
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        original_filename = os.path.basename(file.filename)
        base_name = os.path.splitext(original_filename)[0]
        filename = f"{timestamp}_{base_name}.jpg"

    file_path = os.path.join(directory, filename)
    logger.info(f"Target file path: {file_path}")

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    try:
        # 读取上传的图片数据
        file.file.seek(0)
        image_data = file.file.read()
        logger.info(f"Read {len(image_data)} bytes from uploaded file")

        # 使用PIL打开图片
        with Image.open(io.BytesIO(image_data)) as img:
            logger.info(f"Original image info - Mode: {img.mode}, Size: {img.size}, Format: {img.format}")

            # 使用与detect_faces相同的处理逻辑
            # 处理各种图像模式
            if img.mode in ('RGBA', 'LA'):
                # 处理透明通道
                logger.info(f"Converting {img.mode} to RGB with white background")
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])
                else:  # LA
                    background.paste(img.convert('RGBA'), mask=img.split()[-1])
                img = background
            elif img.mode == 'P':
                # 处理调色板模式
                logger.info("Converting palette image to RGB")
                if 'transparency' in img.info:
                    img = img.convert('RGBA')
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                else:
                    img = img.convert('RGB')
            elif img.mode in ('CMYK', 'LAB', 'YCbCr', 'HSV'):
                # 处理其他颜色模式
                logger.info(f"Converting {img.mode} to RGB")
                img = img.convert('RGB')
            elif img.mode in ('L', '1'):
                # 处理灰度和1位图像
                logger.info(f"Converting {img.mode} to RGB")
                img = img.convert('RGB')
            elif img.mode not in ('RGB',):
                # 其他未知模式，强制转换为RGB
                logger.info(f"Converting unknown mode {img.mode} to RGB")
                img = img.convert('RGB')

            # 处理EXIF方向
            try:
                img = ImageOps.exif_transpose(img)
            except Exception as e:
                logger.warning(f"Could not apply EXIF orientation: {e}")

            # 调整大小（如果图片太大）
            max_dimension = 1500
            if max(img.size) > max_dimension:
                logger.info(f"Image too large {img.size}, resizing...")
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.LANCZOS)
                logger.info(f"Resized to {img.size}")

            # 保存为标准JPEG格式
            logger.info(f"Saving processed image to {file_path}")
            img.save(file_path, format='JPEG', quality=95, optimize=True)
            logger.info(f"Successfully saved processed image to {file_path}")

            # 验证保存的图像
            with Image.open(file_path) as saved_img:
                logger.info(f"Saved image verified - Mode: {saved_img.mode}, Size: {saved_img.size}")
                if saved_img.mode != 'RGB':
                    logger.warning(f"Saved image is not RGB: {saved_img.mode}")
                    # 如果保存的图像不是RGB，重新保存
                    saved_img.convert('RGB').save(file_path, format='JPEG', quality=95, optimize=True)
                    logger.info("Re-saved image as RGB")

    except Exception as e:
        logger.error(f"Error during image processing: {str(e)}")
        # 清理任何失败的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise

    finally:
        # 重置文件指针
        file.file.seek(0)

    return file_path


def detect_faces(image_path: str) -> List[Dict[str, Any]]:
    """
    检测图片中的人脸并返回人脸信息列表
    使用临时文件方法确保图像格式兼容face_recognition库

    Args:
        image_path: 图片路径

    Returns:
        List[Dict[str, Any]]: 人脸信息列表，每个人脸包含位置和编码

    Raises:
        NoFaceDetectedException: 如果未检测到人脸
    """
    logger.info(f"Starting face detection for image: {image_path}")
    temp_path = None

    try:
        # 创建临时文件路径
        temp_path = f"{image_path}_face_detection_temp.jpg"

        # 使用PIL打开图像并进行预处理
        with Image.open(image_path) as img:
            logger.info(f"Original image info - Mode: {img.mode}, Size: {img.size}, Format: {img.format}")

            # 强制转换为标准RGB格式并保存为临时文件
            logger.info("Converting image to standard RGB format...")

            # 处理各种图像模式
            if img.mode in ('RGBA', 'LA'):
                # 处理透明通道
                logger.info(f"Converting {img.mode} to RGB with white background")
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])
                else:  # LA
                    background.paste(img.convert('RGBA'), mask=img.split()[-1])
                img = background
            elif img.mode == 'P':
                # 处理调色板模式
                logger.info("Converting palette image to RGB")
                if 'transparency' in img.info:
                    img = img.convert('RGBA')
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                else:
                    img = img.convert('RGB')
            elif img.mode in ('CMYK', 'LAB', 'YCbCr', 'HSV'):
                # 处理其他颜色模式
                logger.info(f"Converting {img.mode} to RGB")
                img = img.convert('RGB')
            elif img.mode in ('L', '1'):
                # 处理灰度和1位图像
                logger.info(f"Converting {img.mode} to RGB")
                img = img.convert('RGB')
            elif img.mode not in ('RGB',):
                # 其他未知模式，强制转换为RGB
                logger.info(f"Converting unknown mode {img.mode} to RGB")
                img = img.convert('RGB')

            # 处理EXIF方向
            try:
                img = ImageOps.exif_transpose(img)
            except Exception as e:
                logger.warning(f"Could not apply EXIF orientation: {e}")

            # 调整大小（如果太大）
            max_dimension = 1500
            if max(img.size) > max_dimension:
                logger.info(f"Resizing large image from {img.size}")
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.LANCZOS)
                logger.info(f"Resized to {img.size}")

            # 保存为标准JPEG格式
            logger.info(f"Saving processed image to temporary file: {temp_path}")
            img.save(temp_path, format='JPEG', quality=95, optimize=True)

            # 验证保存的图像
            with Image.open(temp_path) as check_img:
                logger.info(f"Temp image verified - Mode: {check_img.mode}, Size: {check_img.size}")
                if check_img.mode != 'RGB':
                    raise InvalidFileTypeException(f"Temp image is not RGB: {check_img.mode}")

        # 使用OpenCV加载图像，这是face_recognition库最兼容的方式
        logger.info("Loading image with OpenCV...")
        image_array = cv2.imread(temp_path)

        if image_array is None:
            raise InvalidFileTypeException(f"Could not load image with OpenCV: {temp_path}")

        # OpenCV默认使用BGR格式，需要转换为RGB
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        logger.info(f"Loaded image array - Shape: {image_array.shape}, dtype: {image_array.dtype}, range: [{image_array.min()}, {image_array.max()}]")

        # 最终验证
        if image_array.dtype != np.uint8:
            logger.error(f"Loaded image is not uint8: {image_array.dtype}")
            raise InvalidFileTypeException(f"Loaded image is not uint8: {image_array.dtype}")

        if len(image_array.shape) != 3 or image_array.shape[2] != 3:
            logger.error(f"Loaded image is not RGB: shape={image_array.shape}")
            raise InvalidFileTypeException(f"Loaded image is not RGB: shape={image_array.shape}")

        # 确保数组是连续的内存布局
        if not image_array.flags['C_CONTIGUOUS']:
            logger.info("Making array contiguous...")
            image_array = np.ascontiguousarray(image_array)

        # 检测人脸位置
        logger.info("Detecting face locations...")
        logger.info(f"Array details before face_recognition call:")
        logger.info(f"  - Shape: {image_array.shape}")
        logger.info(f"  - Dtype: {image_array.dtype}")
        logger.info(f"  - Min/Max: [{image_array.min()}, {image_array.max()}]")
        logger.info(f"  - C_CONTIGUOUS: {image_array.flags['C_CONTIGUOUS']}")
        logger.info(f"  - F_CONTIGUOUS: {image_array.flags['F_CONTIGUOUS']}")
        logger.info(f"  - OWNDATA: {image_array.flags['OWNDATA']}")
        logger.info(f"  - WRITEABLE: {image_array.flags['WRITEABLE']}")

        # 尝试多种方法来确保数组格式正确
        try:
            # 方法1：直接调用
            logger.info("Attempting method 1: Direct call...")
            face_locations = face_recognition.face_locations(image_array)
        except Exception as e1:
            logger.warning(f"Method 1 failed: {e1}")
            try:
                # 方法2：创建新的连续数组
                logger.info("Attempting method 2: Creating new contiguous array...")
                new_array = np.array(image_array, dtype=np.uint8, order='C')
                logger.info(f"New array - Shape: {new_array.shape}, dtype: {new_array.dtype}, C_CONTIGUOUS: {new_array.flags['C_CONTIGUOUS']}")
                face_locations = face_recognition.face_locations(new_array)
                image_array = new_array  # 更新引用以便后续使用
            except Exception as e2:
                logger.warning(f"Method 2 failed: {e2}")
                try:
                    # 方法3：使用copy
                    logger.info("Attempting method 3: Using array copy...")
                    copied_array = image_array.copy()
                    logger.info(f"Copied array - Shape: {copied_array.shape}, dtype: {copied_array.dtype}, C_CONTIGUOUS: {copied_array.flags['C_CONTIGUOUS']}")
                    face_locations = face_recognition.face_locations(copied_array)
                    image_array = copied_array  # 更新引用以便后续使用
                except Exception as e3:
                    logger.warning(f"Method 3 failed: {e3}")
                    try:
                        # 方法4：重新加载图像使用face_recognition.load_image_file
                        logger.info("Attempting method 4: Using face_recognition.load_image_file...")
                        fr_image = face_recognition.load_image_file(temp_path)
                        logger.info(f"FR loaded image - Shape: {fr_image.shape}, dtype: {fr_image.dtype}")
                        face_locations = face_recognition.face_locations(fr_image)
                        image_array = fr_image  # 更新引用以便后续使用
                    except Exception as e4:
                        logger.error(f"All methods failed. Last error: {e4}")
                        raise e4

        # 如果没有检测到人脸，抛出异常
        if not face_locations:
            logger.warning(f"No faces detected in {image_path}")
            raise NoFaceDetectedException()

        # 提取人脸特征
        logger.info("Extracting face encodings...")
        face_encodings = face_recognition.face_encodings(image_array, face_locations)

        # 构建人脸信息列表
        faces = []
        for i, (location, encoding) in enumerate(zip(face_locations, face_encodings)):
            faces.append({
                "id": i,
                "location": location,  # (top, right, bottom, left)
                "encoding": encoding
            })

        logger.info(f"Successfully detected {len(faces)} faces in {image_path}")
        return faces

    except NoFaceDetectedException:
        raise
    except Exception as e:
        logger.error(f"Error detecting faces in {image_path}: {str(e)}")
        raise
    finally:
        # 清理临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
                logger.info(f"Cleaned up temporary file: {temp_path}")
            except Exception as e:
                logger.warning(f"Could not remove temporary file {temp_path}: {e}")


def serialize_encoding(encoding: np.ndarray) -> bytes:
    """
    将人脸编码序列化为二进制数据

    Args:
        encoding: 人脸编码

    Returns:
        bytes: 序列化后的二进制数据
    """
    return pickle.dumps(encoding)


def deserialize_encoding(data: bytes) -> np.ndarray:
    """
    将二进制数据反序列化为人脸编码

    Args:
        data: 序列化的二进制数据

    Returns:
        np.ndarray: 人脸编码
    """
    return pickle.loads(data)


def compare_faces(known_encoding: np.ndarray, unknown_encoding: np.ndarray) -> Tuple[bool, float]:
    """
    比较两个人脸编码，返回是否匹配和相似度

    Args:
        known_encoding: 已知人脸编码
        unknown_encoding: 未知人脸编码

    Returns:
        Tuple[bool, float]: (是否匹配, 相似度)
    """
    # 计算人脸距离
    face_distance = face_recognition.face_distance([known_encoding], unknown_encoding)[0]

    # 转换为相似度（0-1之间，越大越相似）
    similarity = 1 - face_distance

    # 判断是否匹配（阈值可调整）
    is_match = similarity > settings.FACE_SIMILARITY_THRESHOLD

    return is_match, similarity


def crop_face(image_path: str, face_location: Tuple[int, int, int, int], output_path: str) -> None:
    """
    裁剪图片中的人脸并保存

    Args:
        image_path: 原图片路径
        face_location: 人脸位置 (top, right, bottom, left)
        output_path: 输出路径
    """
    # 加载图片
    image = Image.open(image_path)

    # 人脸位置（top, right, bottom, left）
    top, right, bottom, left = face_location

    # 裁剪人脸
    face_image = image.crop((left, top, right, bottom))

    # 保存裁剪后的人脸
    face_image.save(output_path)

    logger.info(f"Cropped face from {image_path} to {output_path}")


def preprocess_image_for_face_recognition(img: Image.Image) -> np.ndarray:
    """
    强化的图像预处理函数，确保图像格式满足face_recognition库的要求
    自动处理各种图像格式、位深度、颜色模式等问题

    Args:
        img: PIL图像对象

    Returns:
        np.ndarray: 预处理后的图像数组，格式为uint8的RGB

    Raises:
        InvalidFileTypeException: 如果图像无法处理
    """
    logger.info(f"Starting enhanced image preprocessing - Mode: {img.mode}, size: {img.size}, format: {img.format}")

    try:
        # 步骤1：处理各种颜色模式和位深度

        # 处理16位图像
        if img.mode in ('I;16', 'I;16L', 'I;16B'):
            logger.info(f"Converting 16-bit image from {img.mode} to 8-bit")
            img_array = np.array(img)
            img_array = (img_array / 256).astype(np.uint8)
            img = Image.fromarray(img_array)
            img = img.convert('RGB')

        # 处理32位浮点图像
        elif img.mode in ('F',):
            logger.info(f"Converting 32-bit float image from {img.mode}")
            img_array = np.array(img)
            img_array = ((img_array - img_array.min()) / (img_array.max() - img_array.min()) * 255).astype(np.uint8)
            img = Image.fromarray(img_array)
            img = img.convert('RGB')

        # 处理CMYK模式
        elif img.mode == 'CMYK':
            logger.info("Converting CMYK image to RGB")
            img = img.convert('RGB')

        # 处理LAB模式
        elif img.mode == 'LAB':
            logger.info("Converting LAB image to RGB")
            img = img.convert('RGB')

        # 处理YCbCr模式
        elif img.mode == 'YCbCr':
            logger.info("Converting YCbCr image to RGB")
            img = img.convert('RGB')

        # 处理HSV模式
        elif img.mode == 'HSV':
            logger.info("Converting HSV image to RGB")
            img = img.convert('RGB')

        # 处理透明通道
        elif img.mode in ('RGBA', 'LA'):
            logger.info(f"Converting {img.mode} image to RGB with white background")
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'RGBA':
                background.paste(img, mask=img.split()[-1])
            else:  # LA
                background.paste(img.convert('RGBA'), mask=img.split()[-1])
            img = background

        # 处理调色板模式
        elif img.mode == 'P':
            logger.info("Converting palette image")
            if 'transparency' in img.info:
                logger.info("Palette image has transparency, converting to RGBA first")
                img = img.convert('RGBA')
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                img = background
            else:
                img = img.convert('RGB')

        # 处理1位图像
        elif img.mode == '1':
            logger.info("Converting 1-bit image to RGB")
            img = img.convert('RGB')

        # 处理灰度图像
        elif img.mode in ('L',):
            logger.info(f"Converting grayscale image {img.mode} to RGB")
            img = img.convert('RGB')

        # 确保最终是RGB模式
        if img.mode != 'RGB':
            logger.info(f"Final conversion from {img.mode} to RGB")
            img = img.convert('RGB')

        # 步骤2：处理EXIF方向
        try:
            img = ImageOps.exif_transpose(img)
            logger.info("Applied EXIF orientation correction")
        except Exception as e:
            logger.warning(f"Could not apply EXIF orientation: {e}")

        # 步骤3：调整大小（如果图片太大）
        max_dimension = 1500
        if max(img.size) > max_dimension:
            logger.info(f"Image too large {img.size}, resizing...")
            ratio = max_dimension / max(img.size)
            new_size = tuple(int(dim * ratio) for dim in img.size)
            img = img.resize(new_size, Image.LANCZOS)
            logger.info(f"Resized to {img.size}")

        # 步骤4：确保图像有足够的对比度
        try:
            extrema = img.getextrema()
            logger.info(f"Image extrema values: {extrema}")

            low_contrast_channels = [i for i, (min_val, max_val) in enumerate(extrema) if max_val - min_val < 30]
            if low_contrast_channels:
                logger.info(f"Enhancing contrast for low contrast channels: {low_contrast_channels}")
                img = ImageOps.autocontrast(img, cutoff=1)
        except Exception as e:
            logger.warning(f"Could not enhance contrast: {e}")

        # 步骤5：转换为numpy数组
        img_array = np.array(img)
        logger.info(f"Converted to array - Shape: {img_array.shape}, dtype: {img_array.dtype}")

        # 步骤6：确保数组类型是uint8
        if img_array.dtype != np.uint8:
            logger.info(f"Converting array from {img_array.dtype} to uint8")
            if img_array.dtype in [np.float32, np.float64]:
                if img_array.max() <= 1.0:
                    img_array = (img_array * 255).astype(np.uint8)
                else:
                    img_array = ((img_array - img_array.min()) /
                               (img_array.max() - img_array.min()) * 255).astype(np.uint8)
            elif img_array.dtype in [np.uint16, np.int16]:
                img_array = (img_array / 256).astype(np.uint8)
            elif img_array.dtype in [np.uint32, np.int32]:
                img_array = (img_array / 16777216).astype(np.uint8)
            else:
                img_array = img_array.astype(np.uint8)

            logger.info(f"Array converted - New dtype: {img_array.dtype}, min: {img_array.min()}, max: {img_array.max()}")

        # 步骤7：确保形状正确
        if len(img_array.shape) == 2:
            logger.info("Converting grayscale array to RGB")
            img_array = np.stack((img_array,) * 3, axis=-1)
        elif len(img_array.shape) == 3:
            if img_array.shape[2] == 1:
                logger.info("Converting single channel to RGB")
                img_array = np.repeat(img_array, 3, axis=2)
            elif img_array.shape[2] == 4:
                logger.info("Removing alpha channel from RGBA")
                img_array = img_array[:, :, :3]
            elif img_array.shape[2] != 3:
                raise InvalidFileTypeException(f"Unsupported number of channels: {img_array.shape[2]}")
        else:
            raise InvalidFileTypeException(f"Invalid image shape: {img_array.shape}")

        # 步骤8：最终验证
        if img_array.shape[2] != 3:
            raise InvalidFileTypeException(f"Final image is not RGB: shape={img_array.shape}")

        if img_array.dtype != np.uint8:
            raise InvalidFileTypeException(f"Final image is not uint8: dtype={img_array.dtype}")

        if img_array.min() == img_array.max():
            logger.warning("Image has no variation (solid color)")
            raise InvalidFileTypeException("Image has no variation (solid color)")

        logger.info(f"Successfully preprocessed image - Final shape: {img_array.shape}, dtype: {img_array.dtype}, range: [{img_array.min()}, {img_array.max()}]")
        return img_array

    except InvalidFileTypeException:
        raise
    except Exception as e:
        logger.error(f"Error during enhanced image preprocessing: {str(e)}")
        raise InvalidFileTypeException(f"Failed to preprocess image: {str(e)}")
