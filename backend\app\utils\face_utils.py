import os
import io
import pickle
import shutil
import logging
import numpy as np
from datetime import datetime
from typing import List, Tuple, Optional, Dict, Any
from PIL import Image, ImageOps

import face_recognition
import numpy as np
from PIL import Image
from fastapi import UploadFile

from app.core.config import settings
from app.core.exceptions import (
    NoFaceDetectedException,
    InvalidFileTypeException,
    FileTooLargeException
)

# 设置日志记录器
logger = logging.getLogger(__name__)


def validate_image(file: UploadFile) -> None:
    """
    验证上传的图片

    检查文件类型、大小和格式，确保图片可以被face_recognition处理

    Args:
        file: 上传的文件

    Raises:
        InvalidFileTypeException: 如果文件类型不支持或图片格式无效
        FileTooLargeException: 如果文件过大
    """
    # 检查文件类型
    ext = file.filename.split(".")[-1].lower()
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise InvalidFileTypeException(
            f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )

    # 检查文件大小
    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)
    
    if file_size > settings.MAX_UPLOAD_SIZE:
        max_size_mb = settings.MAX_UPLOAD_SIZE / (1024 * 1024)
        raise FileTooLargeException(
            f"File too large. Maximum size: {max_size_mb:.1f}MB"
        )
    
    # 尝试用 PIL 打开图片以验证格式
    try:
        image_data = file.file.read()
        img = Image.open(io.BytesIO(image_data))
        if img.format not in ['JPEG', 'PNG']:
            raise InvalidFileTypeException("Image must be in JPEG or PNG format")
        file.file.seek(0)  # 重置文件指针
    except Exception as e:
        raise InvalidFileTypeException(f"Invalid image format: {str(e)}")

    if file_size > settings.MAX_UPLOAD_SIZE:
        max_size_mb = settings.MAX_UPLOAD_SIZE / (1024 * 1024)
        raise FileTooLargeException(
            f"File too large. Maximum size: {max_size_mb:.1f}MB"
        )


def save_uploaded_image(file: UploadFile, directory: str, filename: Optional[str] = None) -> str:
    """
    保存上传的图片到指定目录，确保图片格式正确

    Args:
        file: 上传的文件
        directory: 保存目录
        filename: 文件名（可选）

    Returns:
        str: 保存的文件路径
    """
    # 验证图片
    validate_image(file)

    if filename is None:
        # 使用时间戳作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 统一使用jpg作为保存格式
        # 提取文件名，避免使用反斜杠
        original_filename = os.path.basename(file.filename)
        base_name = os.path.splitext(original_filename)[0]
        filename = f"{timestamp}_{base_name}.jpg"

    file_path = os.path.join(directory, filename)

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 读取上传的图片数据
    image_data = file.file.read()
    
    try:
        # 使用PIL打开图片并进行预处理
        with Image.open(io.BytesIO(image_data)) as img:
            # 转换为RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 标准化图像
            img = ImageOps.exif_transpose(img)  # 处理EXIF方向
            img = ImageOps.autocontrast(img)    # 自动对比度
            
            # 保存为JPEG格式
            img.save(file_path, 'JPEG', quality=95)
            
        logger.info(f"Saved and processed image to {file_path}")
    except Exception as e:
        logger.error(f"Error processing image: {str(e)}")
        raise

    file.file.seek(0)  # 重置文件指针，以防后续还需要使用

    logger.info(f"Saved uploaded image to {file_path}")
    return file_path


def detect_faces(image_path: str) -> List[Dict[str, Any]]:
    """
    检测图片中的人脸并返回人脸信息列表

    Args:
        image_path: 图片路径

    Returns:
        List[Dict[str, Any]]: 人脸信息列表，每个人脸包含位置和编码

    Raises:
        NoFaceDetectedException: 如果未检测到人脸
    """
    logger.info(f"Opening image: {image_path}")
    # 使用 PIL 打开并确保图片格式正确
    with Image.open(image_path) as img:
        # 记录原始图像信息
        logger.info(f"Original image mode: {img.mode}, size: {img.size}, format: {img.format}")
        
        # 转换为 RGB 模式（如果不是的话）
        if img.mode != 'RGB':
            logger.info(f"Converting image from {img.mode} to RGB")
            img = img.convert('RGB')
        
        # 标准化图像
        logger.info("Normalizing image...")
        img = ImageOps.exif_transpose(img)  # 处理EXIF方向
        img = ImageOps.autocontrast(img)    # 自动对比度
        
        # 转换为 NumPy 数组
        logger.info("Converting to NumPy array...")
        img_array = np.array(img)
        
        # 记录数组信息
        logger.info(f"NumPy array shape: {img_array.shape}, dtype: {img_array.dtype}")
        
        # 确保图像是 8 位整数类型
        if img_array.dtype != np.uint8:
            logger.info(f"Converting array from {img_array.dtype} to uint8")
            img_array = img_array.astype(np.uint8)
        
        try:
            # 检测人脸位置
            face_locations = face_recognition.face_locations(img_array)
            
            # 如果没有检测到人脸，抛出异常
            if not face_locations:
                raise NoFaceDetectedException()
            
            # 提取人脸特征
            face_encodings = face_recognition.face_encodings(img_array, face_locations)
            
            # 构建人脸信息列表
            faces = []
            for i, (location, encoding) in enumerate(zip(face_locations, face_encodings)):
                faces.append({
                    "id": i,
                    "location": location,  # (top, right, bottom, left)
                    "encoding": encoding
                })
            
            logger.info(f"Detected {len(faces)} faces in {image_path}")
            return faces
            
        except Exception as e:
            logger.error(f"Error detecting faces in {image_path}: {str(e)}")
            raise

    # 如果没有检测到人脸，抛出异常
    if not face_locations:
        raise NoFaceDetectedException()

    # 提取人脸特征
    face_encodings = face_recognition.face_encodings(image, face_locations)

    # 构建人脸信息列表
    faces = []
    for i, (location, encoding) in enumerate(zip(face_locations, face_encodings)):
        faces.append({
            "id": i,
            "location": location,  # (top, right, bottom, left)
            "encoding": encoding
        })

    logger.info(f"Detected {len(faces)} faces in {image_path}")
    return faces


def serialize_encoding(encoding: np.ndarray) -> bytes:
    """
    将人脸编码序列化为二进制数据

    Args:
        encoding: 人脸编码

    Returns:
        bytes: 序列化后的二进制数据
    """
    return pickle.dumps(encoding)


def deserialize_encoding(data: bytes) -> np.ndarray:
    """
    将二进制数据反序列化为人脸编码

    Args:
        data: 序列化的二进制数据

    Returns:
        np.ndarray: 人脸编码
    """
    return pickle.loads(data)


def compare_faces(known_encoding: np.ndarray, unknown_encoding: np.ndarray) -> Tuple[bool, float]:
    """
    比较两个人脸编码，返回是否匹配和相似度

    Args:
        known_encoding: 已知人脸编码
        unknown_encoding: 未知人脸编码

    Returns:
        Tuple[bool, float]: (是否匹配, 相似度)
    """
    # 计算人脸距离
    face_distance = face_recognition.face_distance([known_encoding], unknown_encoding)[0]

    # 转换为相似度（0-1之间，越大越相似）
    similarity = 1 - face_distance

    # 判断是否匹配（阈值可调整）
    is_match = similarity > settings.FACE_SIMILARITY_THRESHOLD

    return is_match, similarity


def crop_face(image_path: str, face_location: Tuple[int, int, int, int], output_path: str) -> None:
    """
    裁剪图片中的人脸并保存

    Args:
        image_path: 原图片路径
        face_location: 人脸位置 (top, right, bottom, left)
        output_path: 输出路径
    """
    # 加载图片
    image = Image.open(image_path)

    # 人脸位置（top, right, bottom, left）
    top, right, bottom, left = face_location

    # 裁剪人脸
    face_image = image.crop((left, top, right, bottom))

    # 保存裁剪后的人脸
    face_image.save(output_path)

    logger.info(f"Cropped face from {image_path} to {output_path}")
