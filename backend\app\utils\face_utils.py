import os
import io
import pickle
import shutil
import logging
from datetime import datetime
from typing import List, Tuple, Optional, Dict, Any

import numpy as np
import face_recognition
from PIL import Image, ImageOps
from fastapi import UploadFile

from app.core.config import settings
from app.core.exceptions import (
    NoFaceDetectedException,
    InvalidFileTypeException,
    FileTooLargeException
)

# 设置日志记录器
logger = logging.getLogger(__name__)


def validate_image(file: UploadFile) -> None:
    """
    验证上传的图片

    检查文件类型、大小和格式，确保图片可以被face_recognition处理

    Args:
        file: 上传的文件

    Raises:
        InvalidFileTypeException: 如果文件类型不支持或图片格式无效
        FileTooLargeException: 如果文件过大
    """
    # 检查文件类型
    ext = file.filename.split(".")[-1].lower()
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise InvalidFileTypeException(
            f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )

    # 检查文件大小
    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)

    if file_size > settings.MAX_UPLOAD_SIZE:
        max_size_mb = settings.MAX_UPLOAD_SIZE / (1024 * 1024)
        raise FileTooLargeException(
            f"File too large. Maximum size: {max_size_mb:.1f}MB"
        )

    # 尝试用 PIL 打开图片以验证格式
    try:
        image_data = file.file.read()
        img = Image.open(io.BytesIO(image_data))
        if img.format not in ['JPEG', 'PNG']:
            raise InvalidFileTypeException("Image must be in JPEG or PNG format")
        file.file.seek(0)  # 重置文件指针
    except Exception as e:
        file.file.seek(0)  # 重置文件指针
        raise InvalidFileTypeException(f"Invalid image format: {str(e)}")


def save_uploaded_image(file: UploadFile, directory: str, filename: Optional[str] = None) -> str:
    """
    保存上传的图片到指定目录，确保图片格式正确

    Args:
        file: 上传的文件
        directory: 保存目录
        filename: 文件名（可选）

    Returns:
        str: 保存的文件路径
    """
    # 验证图片格式和大小
    logger.info(f"Starting to process uploaded file: {file.filename}")
    validate_image(file)

    # 准备文件路径
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        original_filename = os.path.basename(file.filename)
        base_name = os.path.splitext(original_filename)[0]
        filename = f"{timestamp}_{base_name}.jpg"

    file_path = os.path.join(directory, filename)
    logger.info(f"Target file path: {file_path}")

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    try:
        # 读取上传的图片数据
        file.file.seek(0)
        image_data = file.file.read()
        logger.info(f"Read {len(image_data)} bytes from uploaded file")

        # 使用PIL打开图片
        with Image.open(io.BytesIO(image_data)) as img:
            logger.info(f"Original image info - Mode: {img.mode}, Size: {img.size}, Format: {img.format}")

            # 转换为RGB模式（如果不是的话）
            if img.mode != 'RGB':
                logger.info(f"Converting image from {img.mode} to RGB")
                img = img.convert('RGB')

            # 处理EXIF方向
            img = ImageOps.exif_transpose(img)

            # 调整大小（如果图片太大）
            max_dimension = 1500  # 设置最大尺寸
            if max(img.size) > max_dimension:
                logger.info(f"Image too large {img.size}, resizing...")
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.LANCZOS)
                logger.info(f"Resized to {img.size}")

            # 直接保存为JPEG格式
            logger.info(f"Saving image to {file_path}")
            img.save(file_path, format='JPEG', quality=95, optimize=True)
            logger.info(f"Successfully saved processed image to {file_path}")

    except Exception as e:
        logger.error(f"Error during image processing: {str(e)}")
        # 清理任何失败的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise

    finally:
        # 重置文件指针
        file.file.seek(0)

    return file_path


def detect_faces(image_path: str) -> List[Dict[str, Any]]:
    """
    检测图片中的人脸并返回人脸信息列表

    Args:
        image_path: 图片路径

    Returns:
        List[Dict[str, Any]]: 人脸信息列表，每个人脸包含位置和编码

    Raises:
        NoFaceDetectedException: 如果未检测到人脸
    """
    logger.info(f"Starting face detection for image: {image_path}")
    try:
        # 使用PIL打开图像并进行预处理
        with Image.open(image_path) as img:
            logger.info(f"Original image info - Mode: {img.mode}, Size: {img.size}, Format: {img.format}, Bits: {img.bits if hasattr(img, 'bits') else 'unknown'}")

            # 获取图像位深度信息
            if hasattr(img, 'bits') and img.bits > 8:
                logger.info(f"Image has high bit depth: {img.bits} bits, converting to 8-bit")

            # 转换为RGB模式（如果不是的话）
            if img.mode != 'RGB':
                logger.info(f"Converting image from {img.mode} to RGB")
                img = img.convert('RGB')

            # 处理EXIF方向
            img = ImageOps.exif_transpose(img)

            # 调整大小（如果图片太大）
            max_dimension = 1500  # 设置最大尺寸
            if max(img.size) > max_dimension:
                logger.info(f"Image too large {img.size}, resizing...")
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.LANCZOS)
                logger.info(f"Resized to {img.size}")

            # 保存为临时文件，强制转换为8位RGB
            temp_path = f"{image_path}_temp.jpg"
            img.save(temp_path, format='JPEG', quality=95, optimize=True)
            logger.info(f"Saved preprocessed image to {temp_path}")

            try:
                # 使用PIL重新打开临时文件，确保是8位RGB
                with Image.open(temp_path) as check_img:
                    logger.info(f"Temp image info - Mode: {check_img.mode}, Size: {check_img.size}, Bits: {check_img.bits if hasattr(check_img, 'bits') else '8'}")

                # 使用numpy直接加载图像，确保是8位
                img_array = np.array(Image.open(temp_path))
                logger.info(f"Image array - Shape: {img_array.shape}, dtype: {img_array.dtype}, min: {img_array.min()}, max: {img_array.max()}")

                # 确保是8位整数类型
                if img_array.dtype != np.uint8:
                    logger.info(f"Converting array from {img_array.dtype} to uint8")
                    if img_array.dtype in [np.float32, np.float64]:
                        # 标准化浮点数值到0-255范围
                        img_array = ((img_array - img_array.min()) /
                                   (img_array.max() - img_array.min()) * 255).astype(np.uint8)
                    else:
                        img_array = img_array.astype(np.uint8)

                # 检测人脸位置
                logger.info("Detecting face locations...")
                face_locations = face_recognition.face_locations(img_array)

                # 如果没有检测到人脸，抛出异常
                if not face_locations:
                    logger.warning(f"No faces detected in {image_path}")
                    raise NoFaceDetectedException()

                # 提取人脸特征
                logger.info("Extracting face encodings...")
                face_encodings = face_recognition.face_encodings(img_array, face_locations)

                # 构建人脸信息列表
                faces = []
                for i, (location, encoding) in enumerate(zip(face_locations, face_encodings)):
                    faces.append({
                        "id": i,
                        "location": location,  # (top, right, bottom, left)
                        "encoding": encoding
                    })

                logger.info(f"Successfully detected {len(faces)} faces in {image_path}")
                return faces
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"Removed temporary file {temp_path}")

    except NoFaceDetectedException:
        raise
    except Exception as e:
        logger.error(f"Error detecting faces in {image_path}: {str(e)}")
        raise


def serialize_encoding(encoding: np.ndarray) -> bytes:
    """
    将人脸编码序列化为二进制数据

    Args:
        encoding: 人脸编码

    Returns:
        bytes: 序列化后的二进制数据
    """
    return pickle.dumps(encoding)


def deserialize_encoding(data: bytes) -> np.ndarray:
    """
    将二进制数据反序列化为人脸编码

    Args:
        data: 序列化的二进制数据

    Returns:
        np.ndarray: 人脸编码
    """
    return pickle.loads(data)


def compare_faces(known_encoding: np.ndarray, unknown_encoding: np.ndarray) -> Tuple[bool, float]:
    """
    比较两个人脸编码，返回是否匹配和相似度

    Args:
        known_encoding: 已知人脸编码
        unknown_encoding: 未知人脸编码

    Returns:
        Tuple[bool, float]: (是否匹配, 相似度)
    """
    # 计算人脸距离
    face_distance = face_recognition.face_distance([known_encoding], unknown_encoding)[0]

    # 转换为相似度（0-1之间，越大越相似）
    similarity = 1 - face_distance

    # 判断是否匹配（阈值可调整）
    is_match = similarity > settings.FACE_SIMILARITY_THRESHOLD

    return is_match, similarity


def crop_face(image_path: str, face_location: Tuple[int, int, int, int], output_path: str) -> None:
    """
    裁剪图片中的人脸并保存

    Args:
        image_path: 原图片路径
        face_location: 人脸位置 (top, right, bottom, left)
        output_path: 输出路径
    """
    # 加载图片
    image = Image.open(image_path)

    # 人脸位置（top, right, bottom, left）
    top, right, bottom, left = face_location

    # 裁剪人脸
    face_image = image.crop((left, top, right, bottom))

    # 保存裁剪后的人脸
    face_image.save(output_path)

    logger.info(f"Cropped face from {image_path} to {output_path}")


def preprocess_image(img: Image.Image) -> np.ndarray:
    """
    预处理图像，确保其格式满足face_recognition库的要求
    会主动转换不满足条件的图片格式

    Args:
        img: PIL图像对象

    Returns:
        np.ndarray: 预处理后的图像数组，格式为uint8的RGB
    """
    logger.info(f"Preprocessing image - Original mode: {img.mode}, size: {img.size}, format: {img.format}")

    try:
        # 步骤1：处理透明通道
        if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
            logger.info("Converting image with transparency to RGB with white background")
            # 创建白色背景
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'P':
                img = img.convert('RGBA')
            # 将图片复合到白色背景上
            background.paste(img, mask=img.split()[-1])
            img = background

        # 步骤2：转换为RGB模式
        if img.mode != 'RGB':
            logger.info(f"Converting image from {img.mode} to RGB")
            img = img.convert('RGB')

        # 步骤3：处理EXIF方向
        img = ImageOps.exif_transpose(img)

        # 步骤4：调整大小（如果图片太大）
        max_dimension = 1500  # 设置最大尺寸
        if max(img.size) > max_dimension:
            logger.info(f"Image too large {img.size}, resizing...")
            ratio = max_dimension / max(img.size)
            new_size = tuple(int(dim * ratio) for dim in img.size)
            img = img.resize(new_size, Image.LANCZOS)
            logger.info(f"Resized to {img.size}")

        # 步骤5：调整对比度和亮度（如果需要）
        extrema = img.getextrema()
        logger.info(f"Image extrema values: {extrema}")
        if any(max(channel) - min(channel) < 50 for channel in extrema):  # 对比度太低
            logger.info("Enhancing image contrast")
            img = ImageOps.autocontrast(img, cutoff=1)

        # 步骤6：转换为numpy数组
        img_array = np.array(img)
        logger.info(f"Converted to array - Shape: {img_array.shape}, dtype: {img_array.dtype}")

        # 步骤7：确保数组类型是uint8
        if img_array.dtype != np.uint8:
            logger.info(f"Converting array from {img_array.dtype} to uint8")
            if img_array.dtype in [np.float32, np.float64]:
                # 标准化浮点数值到0-255范围
                img_array = ((img_array - img_array.min()) /
                           (img_array.max() - img_array.min()) * 255).astype(np.uint8)
            else:
                img_array = img_array.astype(np.uint8)
            logger.info(f"Array converted - New dtype: {img_array.dtype}, min: {img_array.min()}, max: {img_array.max()}")

        # 步骤8：确保形状正确
        if len(img_array.shape) == 2:  # 灰度图像
            logger.info("Converting grayscale to RGB")
            img_array = np.stack((img_array,) * 3, axis=-1)
        elif len(img_array.shape) != 3 or img_array.shape[2] != 3:
            raise InvalidFileTypeException(f"Invalid image shape: {img_array.shape}")

        # 步骤9：检查并修复异常值
        if img_array.min() == img_array.max():  # 全黑或全白图片
            logger.warning("Image has no variation (solid color)")
            raise InvalidFileTypeException("Image has no variation (solid color)")

        logger.info(f"Final array shape: {img_array.shape}, dtype: {img_array.dtype}, range: [{img_array.min()}, {img_array.max()}]")
        return img_array

    except Exception as e:
        logger.error(f"Error during image preprocessing: {str(e)}")
        raise
