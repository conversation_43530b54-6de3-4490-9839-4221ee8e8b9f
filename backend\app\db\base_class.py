from typing import Any

from sqlalchemy import Column, Integer
from sqlalchemy.ext.declarative import as_declarative, declared_attr


@as_declarative()
class Base:
    """
    SQLAlchemy 声明性基类

    提供自动生成表名和id主键的功能
    """
    __name__: str

    # 定义所有模型共有的列
    id = Column(Integer, primary_key=True, index=True)

    # 根据类名自动生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()
