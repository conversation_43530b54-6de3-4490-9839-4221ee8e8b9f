import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      }
    }
  },
  server: {
    proxy: {
      '/api/v1': {
        target: 'http://localhost:8000', // 后端服务地址
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api\/v1/, ''),
      },
    },
  },
})
