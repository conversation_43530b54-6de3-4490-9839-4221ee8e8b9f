import apiClient from './index';

/**
 * 获取人脸列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的最大记录数
 * @returns {Promise} - 返回人脸列表
 */
export const getFaces = async (params = {}) => {
  const response = await apiClient.get('/faces/', { params });
  return response.data;
};

/**
 * 获取单个人脸
 * @param {number} faceId - 人脸ID
 * @returns {Promise} - 返回人脸信息
 */
export const getFace = async (faceId) => {
  const response = await apiClient.get(`/faces/${faceId}`);
  return response.data;
};

/**
 * 创建新人脸
 * @param {Object} faceData - 人脸数据
 * @param {string} faceData.name - 人物姓名
 * @param {string} faceData.description - 人物描述（可选）
 * @param {File} faceData.file - 人脸图片文件
 * @returns {Promise} - 返回创建的人脸信息
 */
export const createFace = async (faceData) => {
  const formData = new FormData();
  formData.append('name', faceData.name);

  if (faceData.description) {
    formData.append('description', faceData.description);
  }

  formData.append('file', faceData.file);

  const response = await apiClient.post('/faces/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

/**
 * 更新人脸信息
 * @param {number} faceId - 人脸ID
 * @param {Object} faceData - 人脸数据
 * @param {string} faceData.name - 人物姓名
 * @param {string} faceData.description - 人物描述
 * @returns {Promise} - 返回更新后的人脸信息
 */
export const updateFace = async (faceId, faceData) => {
  const response = await apiClient.put(`/faces/${faceId}`, faceData);
  return response.data;
};

/**
 * 删除人脸
 * @param {number} faceId - 人脸ID
 * @returns {Promise} - 返回删除结果
 */
export const deleteFace = async (faceId) => {
  const response = await apiClient.delete(`/faces/${faceId}`);
  return response.data;
};

/**
 * 批量上传人脸
 * @param {Object} data - 上传数据
 * @param {Array<File>} data.files - 人脸图片文件列表
 * @param {Array<number>} data.category_ids - 分类ID列表（可选）
 * @returns {Promise} - 返回批量上传结果
 */
export const bulkUploadFaces = async (data) => {
  const formData = new FormData();

  // 添加多个文件
  data.files.forEach(file => {
    formData.append('files', file);
  });

  // 添加分类ID（如果有）
  if (data.category_ids && data.category_ids.length > 0) {
    data.category_ids.forEach(id => {
      formData.append('category_ids', id);
    });
  }

  const response = await apiClient.post('/faces/bulk', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

/**
 * 更新人脸分类
 * @param {number} faceId - 人脸ID
 * @param {Array<number>} categoryIds - 分类ID列表
 * @returns {Promise} - 返回更新结果
 */
export const updateFaceCategories = async (faceId, categoryIds) => {
  const response = await apiClient.post(`/faces/${faceId}/categories`, categoryIds);
  return response.data;
};

/**
 * 获取人脸别名
 * @param {number} faceId - 人脸ID
 * @returns {Promise} - 返回别名列表
 */
export const getFaceAliases = async (faceId) => {
  const response = await apiClient.get(`/faces/${faceId}/aliases`);
  return response.data;
};

/**
 * 为人脸添加别名
 * @param {number} faceId - 人脸ID
 * @param {Array<Object>} aliases - 别名列表
 * @returns {Promise} - 返回添加结果
 */
export const addFaceAliases = async (faceId, aliases) => {
  const response = await apiClient.post(`/faces/${faceId}/aliases`, aliases);
  return response.data;
};
