import apiClient from './index';

/**
 * 获取人脸列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的最大记录数
 * @returns {Promise} - 返回人脸列表
 */
export const getFaces = async (params = {}) => {
  const response = await apiClient.get('/faces/', { params });
  return response.data;
};

/**
 * 获取单个人脸
 * @param {number} faceId - 人脸ID
 * @returns {Promise} - 返回人脸信息
 */
export const getFace = async (faceId) => {
  const response = await apiClient.get(`/faces/${faceId}`);
  return response.data;
};

/**
 * 创建新人脸
 * @param {Object} faceData - 人脸数据
 * @param {string} faceData.name - 人物姓名
 * @param {string} faceData.description - 人物描述（可选）
 * @param {File} faceData.file - 人脸图片文件
 * @returns {Promise} - 返回创建的人脸信息
 */
export const createFace = async (faceData) => {
  const formData = new FormData();
  formData.append('name', faceData.name);
  
  if (faceData.description) {
    formData.append('description', faceData.description);
  }
  
  formData.append('file', faceData.file);
  
  const response = await apiClient.post('/faces/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

/**
 * 更新人脸信息
 * @param {number} faceId - 人脸ID
 * @param {Object} faceData - 人脸数据
 * @param {string} faceData.name - 人物姓名
 * @param {string} faceData.description - 人物描述
 * @returns {Promise} - 返回更新后的人脸信息
 */
export const updateFace = async (faceId, faceData) => {
  const response = await apiClient.put(`/faces/${faceId}`, faceData);
  return response.data;
};

/**
 * 删除人脸
 * @param {number} faceId - 人脸ID
 * @returns {Promise} - 返回删除结果
 */
export const deleteFace = async (faceId) => {
  const response = await apiClient.delete(`/faces/${faceId}`);
  return response.data;
};
