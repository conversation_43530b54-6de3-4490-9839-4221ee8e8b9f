/*! Element Plus v2.9.10 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleEu = factory());
})(this, (function () { 'use strict';

  var eu = {
    name: "eu",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "Ados",
        clear: "Garbitu"
      },
      datepicker: {
        now: "Orain",
        today: "Gaur",
        cancel: "Utzi",
        clear: "Garbitu",
        confirm: "Ados",
        selectDate: "Hautatu data",
        selectTime: "Hautatu ordua",
        startDate: "Hasierako data",
        startTime: "Hasierako ordua",
        endDate: "Amaierako data",
        endTime: "Amaierako ordua",
        prevYear: "Aurreko urtea",
        nextYear: "Hurrengo urtea",
        prevMonth: "Aurreko hilabetea",
        nextMonth: "Hurrengo hilabetea",
        year: "",
        month1: "Urtarrila",
        month2: "Otsaila",
        month3: "Martxoa",
        month4: "Apirila",
        month5: "Maiatza",
        month6: "Ekaina",
        month7: "Uztaila",
        month8: "Abuztua",
        month9: "Iraila",
        month10: "Urria",
        month11: "Azaroa",
        month12: "Abendua",
        weeks: {
          sun: "ig.",
          mon: "al.",
          tue: "ar.",
          wed: "az.",
          thu: "og.",
          fri: "ol.",
          sat: "lr."
        },
        months: {
          jan: "urt",
          feb: "ots",
          mar: "mar",
          apr: "api",
          may: "mai",
          jun: "eka",
          jul: "uzt",
          aug: "abu",
          sep: "ira",
          oct: "urr",
          nov: "aza",
          dec: "abe"
        }
      },
      select: {
        loading: "Kargatzen",
        noMatch: "Bat datorren daturik ez",
        noData: "Daturik ez",
        placeholder: "Hautatu"
      },
      mention: {
        loading: "Kargatzen"
      },
      cascader: {
        noMatch: "Bat datorren daturik ez",
        loading: "Kargatzen",
        placeholder: "Hautatu",
        noData: "Daturik ez"
      },
      pagination: {
        goto: "Joan",
        pagesize: "/orria",
        total: "Guztira {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "Mezua",
        confirm: "Ados",
        cancel: "Utzi",
        error: "Sarrera baliogabea"
      },
      upload: {
        deleteTip: "sakatu Ezabatu kentzeko",
        delete: "Ezabatu",
        preview: "Aurrebista",
        continue: "Jarraitu"
      },
      table: {
        emptyText: "Daturik ez",
        confirmFilter: "Baieztatu",
        resetFilter: "Berrezarri",
        clearFilter: "Guztia",
        sumText: "Batura"
      },
      tour: {
        next: "Hurrengoa",
        previous: "Aurrekoa",
        finish: "Bukatu"
      },
      tree: {
        emptyText: "Daturik ez"
      },
      transfer: {
        noMatch: "Bat datorren daturik ez",
        noData: "Daturik ez",
        titles: ["Zerrenda 1", "Zerrenda 2"],
        filterPlaceholder: "Sartu gako-hitza",
        noCheckedFormat: "{total} elementu",
        hasCheckedFormat: "{checked}/{total} hautatuta"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return eu;

}));
