from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

# 用户模式
class UserBase(BaseModel):
    username: str
    email: EmailStr

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    is_admin: int
    created_at: datetime
    
    class Config:
        orm_mode = True

# 人脸模式
class FaceBase(BaseModel):
    name: str

class FaceCreate(FaceBase):
    pass

class Face(FaceBase):
    id: int
    image_path: str
    user_id: int
    created_at: datetime
    
    class Config:
        orm_mode = True

# 搜索结果模式
class SearchResultBase(BaseModel):
    face_id: int
    similarity: float

class SearchResult(SearchResultBase):
    id: int
    face: Face
    
    class Config:
        orm_mode = True

# 搜索历史模式
class SearchHistoryBase(BaseModel):
    user_id: Optional[int] = None

class SearchHistory(SearchHistoryBase):
    id: int
    image_path: str
    created_at: datetime
    results: List[SearchResult] = []
    
    class Config:
        orm_mode = True

# 令牌模式
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None
