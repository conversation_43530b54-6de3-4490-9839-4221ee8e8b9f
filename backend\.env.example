# API配置
API_V1_STR=/api/v1

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=10080  # 7天

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:5173", "http://localhost:8080"]

# 项目信息
PROJECT_NAME=Face Recognition API
DESCRIPTION=Face Recognition Search System API
VERSION=0.1.0

# 数据库配置
SQLALCHEMY_DATABASE_URI=sqlite:///./face_recognition.db

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=["jpg", "jpeg", "png"]

# 人脸识别配置
FACE_SIMILARITY_THRESHOLD=0.6

# 日志配置
LOG_LEVEL=INFO
